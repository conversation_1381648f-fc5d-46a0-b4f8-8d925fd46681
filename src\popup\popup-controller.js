/**
 * SVG Switcher Extension - Popup Controller
 * Handles main popup UI logic and user interactions
 */

import {
  ELEMENT_IDS,
  DEFAULT_SETTINGS,
  EXTENSION_CONFIG,
  DEBUG,
} from "../config/constants.js";
import { StorageService } from "../services/storage.service.js";
import { ConversionService } from "../services/conversion.service.js";
import {
  getElementById,
  getValue,
  setValue,
  isChecked,
  setChecked,
  setEnabled,
  hide,
  show,
  setText,
  setHTML,
  getSelectedRadioValue,
  setProgress,
} from "../utils/dom.utils.js";
import {
  validateFilename,
  sanitizeFilename,
} from "../utils/validation.utils.js";
import {
  handleError,
  handleConversionError,
  handleStorageError,
  ERROR_CATEGORIES,
  ERROR_SEVERITY,
} from "../utils/error-handler.utils.js";

/**
 * Main popup controller class
 */
export class PopupController {
  constructor() {
    this.selectedSVGs = [];
    this.initialized = false;
    this.userSettings = {};
    this.pageContext = null;
  }

  /**
   * Initialize the popup controller
   */
  async initialize() {
    if (this.initialized) return;

    try {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Popup controller initializing...`
        );
      }

      // Validate required DOM elements exist
      this.validateRequiredElements();

      await this.loadUserSettings();
      await this.checkForSelectedSVGs();
      await this.loadPresets();
      this.setupEventListeners();
      this.applyUserSettings();
      this.setupStorageListener();

      this.initialized = true;

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Popup controller initialized successfully`
        );
      }
    } catch (error) {
      handleError(
        error,
        ERROR_CATEGORIES.UNKNOWN,
        ERROR_SEVERITY.CRITICAL,
        "Popup controller initialization"
      );
      throw error;
    }
  }

  /**
   * Validate that all required DOM elements exist
   */
  validateRequiredElements() {
    const requiredElements = [
      ELEMENT_IDS.status,
      ELEMENT_IDS.downloadBtn,
      ELEMENT_IDS.copyToClipboardBtn,
      ELEMENT_IDS.filename,
      ELEMENT_IDS.quality,
      ELEMENT_IDS.format,
      ELEMENT_IDS.transparentBg,
      ELEMENT_IDS.bgColor,
    ];

    const missingElements = [];

    for (const elementId of requiredElements) {
      const element = document.getElementById(elementId);
      if (!element) {
        missingElements.push(elementId);
      }
    }

    if (missingElements.length > 0) {
      throw new Error(
        `Missing required DOM elements: ${missingElements.join(", ")}`
      );
    }

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} All required DOM elements found`
      );
    }
  }

  /**
   * Load user settings from storage
   */
  async loadUserSettings() {
    try {
      const stored = await StorageService.get(["settings"], "sync");
      this.userSettings = {
        defaultFilename:
          stored.settings?.defaultFilename || DEFAULT_SETTINGS.filename,
        defaultQuality:
          stored.settings?.defaultQuality || DEFAULT_SETTINGS.quality,
        defaultFormat:
          stored.settings?.defaultFormat || DEFAULT_SETTINGS.format,
        defaultTransparent:
          stored.settings?.defaultTransparent || DEFAULT_SETTINGS.transparentBg,
        defaultBackgroundColor:
          stored.settings?.defaultBackgroundColor || DEFAULT_SETTINGS.bgColor,
        showPreview: stored.settings?.showPreview !== false,
        preserveAspectRatio: stored.settings?.preserveAspectRatio !== false,
        confirmBeforeDownload: stored.settings?.confirmBeforeDownload || false,
      };

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} User settings loaded:`,
          this.userSettings
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to load user settings:`,
        error
      );
      // Use defaults if loading fails
      this.userSettings = {
        defaultFilename: DEFAULT_SETTINGS.filename,
        defaultQuality: DEFAULT_SETTINGS.quality,
        defaultFormat: DEFAULT_SETTINGS.format,
        defaultTransparent: DEFAULT_SETTINGS.transparentBg,
        defaultBackgroundColor: DEFAULT_SETTINGS.bgColor,
        showPreview: true,
        preserveAspectRatio: true,
        confirmBeforeDownload: false,
      };
    }
  }

  /**
   * Set up storage listener for settings changes
   */
  setupStorageListener() {
    if (chrome.storage && chrome.storage.onChanged) {
      this.storageListener = (changes, areaName) => {
        if (areaName === "sync" && changes.settings) {
          this.loadUserSettings().then(() => {
            this.applyUserSettings();
            if (DEBUG.enabled) {
              console.log(
                `${EXTENSION_CONFIG.logPrefix} Popup settings updated from storage`
              );
            }
          });
        }
      };
      chrome.storage.onChanged.addListener(this.storageListener);
    }
  }

  /**
   * Apply user settings to the form
   */
  applyUserSettings() {
    setValue(ELEMENT_IDS.filename, this.userSettings.defaultFilename);
    setValue(ELEMENT_IDS.quality, this.userSettings.defaultQuality);
    setValue(ELEMENT_IDS.bgColor, this.userSettings.defaultBackgroundColor);
    setChecked(ELEMENT_IDS.transparentBg, this.userSettings.defaultTransparent);
    setValue(ELEMENT_IDS.format, this.userSettings.defaultFormat);

    // Handle format-specific UI updates
    this.handleFormatChange(this.userSettings.defaultFormat);
  }

  /**
   * Handle format change - show/hide relevant controls
   * @param {string} format - Selected format
   */
  handleFormatChange(format) {
    const isSVGFormat = format === "svg";

    // Get conversion-related elements
    const qualityControl = document.querySelector(".quality-control");
    const customDimensionsControl = document.querySelector(
      ".custom-dimensions-control"
    );
    const backgroundControl = document.querySelector(".background-control");
    const transparentBgControl = document.querySelector(
      ".transparent-bg-control"
    );

    // For SVG format, hide conversion-related controls
    if (isSVGFormat) {
      if (qualityControl) hide(qualityControl);
      if (customDimensionsControl) hide(customDimensionsControl);
      if (backgroundControl) hide(backgroundControl);
      if (transparentBgControl) hide(transparentBgControl);
    } else {
      // For other formats, show conversion controls
      if (qualityControl) show(qualityControl);
      if (customDimensionsControl) show(customDimensionsControl);
      if (backgroundControl) show(backgroundControl);
      if (transparentBgControl) show(transparentBgControl);

      // Update background color dependency
      const transparentBg = isChecked(ELEMENT_IDS.transparentBg);
      setEnabled(ELEMENT_IDS.bgColor, !transparentBg);
    }

    // Update copy to clipboard button tooltip
    const copyBtn = getElementById(ELEMENT_IDS.copyToClipboardBtn);
    if (copyBtn) {
      const tooltip = isSVGFormat
        ? "Copy SVG text to clipboard (first SVG if multiple selected)"
        : "Copy rasterized image to clipboard (first SVG if multiple selected)";
      copyBtn.setAttribute("title", tooltip);
    }

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Format changed to ${format}, SVG mode: ${isSVGFormat}`
      );
    }
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Download button
    getElementById(ELEMENT_IDS.downloadBtn).addEventListener(
      "click",
      this.handleDownload.bind(this)
    );

    // Copy to clipboard button
    getElementById(ELEMENT_IDS.copyToClipboardBtn).addEventListener(
      "click",
      this.handleCopyToClipboard.bind(this)
    );

    // Filename input
    getElementById(ELEMENT_IDS.filename).addEventListener(
      "input",
      this.validateAndUpdateUI.bind(this)
    );

    // Settings change listeners
    const settingsElements = [
      ELEMENT_IDS.quality,
      ELEMENT_IDS.customWidth,
      ELEMENT_IDS.customHeight,
      ELEMENT_IDS.bgColor,
      ELEMENT_IDS.transparentBg,
      ELEMENT_IDS.format,
    ];

    settingsElements.forEach((id) => {
      getElementById(id).addEventListener("change", () => {
        if (this.selectedSVGs.length > 0) {
          this.updateUI(true);
        }
      });
    });

    // Format change listener - special handling for SVG format
    getElementById(ELEMENT_IDS.format).addEventListener("change", (e) => {
      this.handleFormatChange(e.target.value);
      if (this.selectedSVGs.length > 0) {
        this.updateUI(true);
      }
    });

    // Background color dependency
    getElementById(ELEMENT_IDS.transparentBg).addEventListener(
      "change",
      (e) => {
        setEnabled(ELEMENT_IDS.bgColor, !e.target.checked);
      }
    );

    // Custom dimension linking
    this.setupDimensionLinking();

    // Preset management
    this.setupPresetEventListeners();

    // Settings button
    const settingsBtn = document.getElementById("settingsBtn");
    if (settingsBtn) {
      settingsBtn.addEventListener("click", this.handleOpenSettings.bind(this));
    }

    // Cleanup on window unload
    window.addEventListener("beforeunload", () => this.cleanup());
  }

  /**
   * Clean up resources
   */
  cleanup() {
    // Clean up storage listener
    if (chrome.storage && chrome.storage.onChanged && this.storageListener) {
      chrome.storage.onChanged.removeListener(this.storageListener);
    }
  }

  /**
   * Set up dimension input linking
   */
  setupDimensionLinking() {
    const customWidth = getElementById(ELEMENT_IDS.customWidth);
    const customHeight = getElementById(ELEMENT_IDS.customHeight);

    customWidth.addEventListener("input", () => {
      if (
        this.selectedSVGs.length > 0 &&
        customWidth.value &&
        !customHeight.value
      ) {
        const aspectRatio =
          this.selectedSVGs[0].width / this.selectedSVGs[0].height;
        customHeight.value = Math.round(customWidth.value / aspectRatio);
      }
    });

    customHeight.addEventListener("input", () => {
      if (
        this.selectedSVGs.length > 0 &&
        customHeight.value &&
        !customWidth.value
      ) {
        const aspectRatio =
          this.selectedSVGs[0].width / this.selectedSVGs[0].height;
        customWidth.value = Math.round(customHeight.value * aspectRatio);
      }
    });
  }

  /**
   * Set up preset-related event listeners
   */
  setupPresetEventListeners() {
    const presetSelect = getElementById(ELEMENT_IDS.presetSelect);
    const savePresetBtn = getElementById(ELEMENT_IDS.savePresetBtn);
    const deletePresetBtn = getElementById(ELEMENT_IDS.deletePresetBtn);

    presetSelect.addEventListener("change", (e) => {
      const selected = e.target.value;
      setEnabled(deletePresetBtn, !!selected);
      if (selected) {
        this.loadPreset(selected);
      }
    });

    savePresetBtn.addEventListener("click", this.savePreset.bind(this));

    deletePresetBtn.addEventListener("click", () => {
      const selected = presetSelect.value;
      if (selected && confirm(`Delete preset "${selected}"?`)) {
        this.deletePreset(selected);
      }
    });
  }

  /**
   * Check for selected SVGs on the current tab
   */
  async checkForSelectedSVGs() {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      if (!tab) {
        throw new Error("No active tab found");
      }

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Checking for selected SVGs on tab`,
          tab.id
        );
      }

      // Check if the tab URL is supported (not chrome:// pages, etc.)
      if (
        tab.url &&
        (tab.url.startsWith("chrome://") ||
          tab.url.startsWith("chrome-extension://") ||
          tab.url.startsWith("moz-extension://"))
      ) {
        if (DEBUG.enabled) {
          console.log(
            `${EXTENSION_CONFIG.logPrefix} Skipping unsupported tab URL: ${tab.url}`
          );
        }
        this.selectedSVGs = [];
        this.updateUI(false);
        return;
      }

      // Try to get selection count first with timeout
      const response = await Promise.race([
        chrome.tabs.sendMessage(tab.id, { action: "getSelectionCount" }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Timeout")), 5000)
        ),
      ]);

      if (response && response.count > 0) {
        // Get SVG data with timeout
        const svgDataResponse = await Promise.race([
          chrome.tabs.sendMessage(tab.id, { action: "getSVGData" }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Timeout")), 5000)
          ),
        ]);

        if (
          svgDataResponse &&
          svgDataResponse.success &&
          svgDataResponse.data
        ) {
          this.selectedSVGs = svgDataResponse.data;
          this.pageContext = svgDataResponse.pageContext || null;
          this.updateUI(true);
          return;
        }
      }

      // Fallback: no selections found
      this.selectedSVGs = [];
      this.updateUI(false);
    } catch (error) {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Content script communication failed:`,
          error.message
        );
      }

      // Don't treat content script communication failure as a critical error
      // This is expected on pages where content script can't run
      this.selectedSVGs = [];
      this.updateUI(false);
    }
  }

  /**
   * Update the popup UI based on selection state
   */
  updateUI(hasSelection) {
    const status = getElementById(ELEMENT_IDS.status);
    const svgInfo = getElementById(ELEMENT_IDS.svgInfo);
    const downloadBtn = getElementById(ELEMENT_IDS.downloadBtn);
    const copyToClipboardBtn = getElementById(ELEMENT_IDS.copyToClipboardBtn);
    const batchControls = getElementById(ELEMENT_IDS.batchControls);
    const previewContainer = getElementById(ELEMENT_IDS.previewContainer);

    if (hasSelection && this.selectedSVGs.length > 0) {
      // Update status
      status.className = "status selected";
      setText(
        status,
        `${this.selectedSVGs.length} SVG${
          this.selectedSVGs.length > 1 ? "s" : ""
        } selected!`
      );

      // Show/hide batch controls
      if (this.selectedSVGs.length > 1) {
        show(batchControls);
      } else {
        hide(batchControls);
      }

      // Update SVG info
      this.updateSVGInfo();

      // Update download button state
      this.validateAndUpdateUI();

      // Update preview for single selection (if enabled and not SVG format)
      const currentFormat = getValue(ELEMENT_IDS.format);
      if (
        this.selectedSVGs.length === 1 &&
        this.userSettings.showPreview &&
        currentFormat !== "svg"
      ) {
        this.updatePreview();
      } else {
        hide(previewContainer);
      }
    } else {
      // No selection state
      status.className = "status no-selection";
      setText(status, "No SVG selected");
      hide(svgInfo);
      hide(batchControls);
      hide(previewContainer);
      setEnabled(downloadBtn, false);
      setEnabled(copyToClipboardBtn, false);
    }
  }

  /**
   * Update SVG information display
   */
  updateSVGInfo() {
    const svgInfo = getElementById(ELEMENT_IDS.svgInfo);
    const svgDetails = getElementById(ELEMENT_IDS.svgDetails);

    if (this.selectedSVGs.length === 0) {
      hide(svgInfo);
      return;
    }

    const settings = this.getCurrentSettings();
    const conversionInfo = ConversionService.getConversionInfo(
      this.selectedSVGs[0],
      settings
    );

    let infoHTML = `<strong>Selected:</strong> ${this.selectedSVGs.length} SVG${
      this.selectedSVGs.length > 1 ? "s" : ""
    }<br>`;

    if (this.selectedSVGs.length === 1) {
      infoHTML += `<strong>Size:</strong> ${conversionInfo.originalSize}<br>`;
    }

    infoHTML += `<strong>${conversionInfo.format} Output:</strong> ${
      conversionInfo.outputSize
    } ${
      conversionInfo.isCustomSize ? "(custom)" : `(${conversionInfo.scale})`
    }`;

    setHTML(svgDetails, infoHTML);
    show(svgInfo);

    // Set dimension placeholders
    const customWidth = getElementById(ELEMENT_IDS.customWidth);
    const customHeight = getElementById(ELEMENT_IDS.customHeight);
    customWidth.placeholder = this.selectedSVGs[0].width;
    customHeight.placeholder = this.selectedSVGs[0].height;
  }

  /**
   * Get current settings from the form
   */
  getCurrentSettings() {
    return {
      quality: getValue(ELEMENT_IDS.quality),
      customWidth: getValue(ELEMENT_IDS.customWidth),
      customHeight: getValue(ELEMENT_IDS.customHeight),
      bgColor: getValue(ELEMENT_IDS.bgColor),
      transparentBg: isChecked(ELEMENT_IDS.transparentBg),
      format: getValue(ELEMENT_IDS.format),
    };
  }

  /**
   * Validate filename and update UI accordingly
   */
  validateAndUpdateUI() {
    const filename = getValue(ELEMENT_IDS.filename);
    const downloadBtn = getElementById(ELEMENT_IDS.downloadBtn);
    const copyToClipboardBtn = getElementById(ELEMENT_IDS.copyToClipboardBtn);

    const validation = validateFilename(filename);
    const hasSelection = this.selectedSVGs.length > 0;

    setEnabled(downloadBtn, validation.valid && hasSelection);
    // Clipboard doesn't need filename validation, just needs selection
    setEnabled(copyToClipboardBtn, hasSelection);
  }

  /**
   * Handle download button click
   */
  async handleDownload() {
    try {
      // Check if confirmation is required
      if (this.userSettings.confirmBeforeDownload) {
        const svgCount = this.selectedSVGs.length;
        const message = `Download ${svgCount} SVG${
          svgCount > 1 ? "s" : ""
        } as ${getValue(ELEMENT_IDS.format).toUpperCase()}?`;
        if (!confirm(message)) {
          return; // User cancelled
        }
      }

      const filename = sanitizeFilename(getValue(ELEMENT_IDS.filename));
      const settings = this.getCurrentSettings();
      const downloadType =
        getSelectedRadioValue("downloadType") ||
        this.userSettings.defaultDownloadType ||
        "separate";

      if (downloadType === "zip" && this.selectedSVGs.length > 1) {
        await ConversionService.convertMultipleToZip(
          this.selectedSVGs,
          filename,
          settings,
          this.updateProgress.bind(this),
          this.pageContext
        );
      } else {
        await ConversionService.convertMultipleSeparate(
          this.selectedSVGs,
          filename,
          settings,
          this.updateProgress.bind(this),
          this.pageContext
        );
      }

      // Reset progress after delay
      setTimeout(() => setProgress(0), 1000);
    } catch (error) {
      handleConversionError(error, "File download process");
    }
  }

  /**
   * Handle copy to clipboard button click
   */
  async handleCopyToClipboard() {
    try {
      if (this.selectedSVGs.length === 0) {
        return;
      }

      const settings = this.getCurrentSettings();

      if (this.selectedSVGs.length === 1) {
        // Copy single SVG
        await ConversionService.copyToClipboard(this.selectedSVGs[0], settings);
      } else {
        // Copy first SVG when multiple are selected
        await ConversionService.copyMultipleToClipboard(
          this.selectedSVGs,
          settings
        );
      }

      // Show success feedback
      const status = getElementById(ELEMENT_IDS.status);
      const originalText = status.textContent;
      const message =
        this.selectedSVGs.length > 1
          ? "✓ First SVG copied to clipboard!"
          : "✓ Copied to clipboard!";
      setText(status, message);

      // Reset status after 2 seconds
      setTimeout(() => {
        setText(status, originalText);
      }, 2000);

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Successfully copied ${
            this.selectedSVGs.length > 1
              ? "first of " + this.selectedSVGs.length
              : ""
          } SVG to clipboard`
        );
      }
    } catch (error) {
      // Show error feedback
      const status = getElementById(ELEMENT_IDS.status);
      const originalText = status.textContent;
      setText(status, "❌ Copy failed!");

      // Reset status after 2 seconds
      setTimeout(() => {
        setText(status, originalText);
      }, 2000);

      handleConversionError(error, "Clipboard copy process");
    }
  }

  /**
   * Update progress bar
   */
  updateProgress(percentage) {
    setProgress(percentage);
  }

  /**
   * Handle settings button click
   */
  handleOpenSettings() {
    try {
      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Opening settings page...`);
      }

      chrome.runtime.openOptionsPage();
    } catch (error) {
      handleError(
        error,
        ERROR_CATEGORIES.UNKNOWN,
        ERROR_SEVERITY.MEDIUM,
        "Opening settings page"
      );
    }
  }

  /**
   * Update preview for single SVG
   */
  async updatePreview() {
    const previewContainer = getElementById(ELEMENT_IDS.previewContainer);
    const previewCanvas = getElementById(ELEMENT_IDS.previewCanvas);

    // Check if preview should be shown based on settings
    if (!this.userSettings.showPreview || this.selectedSVGs.length !== 1) {
      hide(previewContainer);
      return;
    }

    try {
      const settings = this.getCurrentSettings();
      const canvas = await ConversionService.generatePreview(
        this.selectedSVGs[0],
        settings
      );

      // Replace the preview canvas
      previewCanvas.width = canvas.width;
      previewCanvas.height = canvas.height;
      const ctx = previewCanvas.getContext("2d");
      ctx.drawImage(canvas, 0, 0);

      show(previewContainer);
    } catch (error) {
      handleConversionError(error, "Preview generation");
      hide(previewContainer);
    }
  }

  /**
   * Load saved presets from storage
   */
  async loadPresets() {
    try {
      const presetSelect = getElementById(ELEMENT_IDS.presetSelect);
      const presetNames = await StorageService.getPresetNames();

      // Clear existing options except the default one
      while (presetSelect.options.length > 1) {
        presetSelect.remove(1);
      }

      // Add saved presets
      presetNames.forEach((name) => {
        const option = document.createElement("option");
        option.value = name;
        option.textContent = name;
        presetSelect.appendChild(option);
      });
    } catch (error) {
      handleStorageError(error, "Loading presets from storage");
    }
  }

  /**
   * Save current settings as a preset
   */
  async savePreset() {
    try {
      const name = getValue(ELEMENT_IDS.presetName).trim();
      if (!name) {
        alert("Please enter a preset name");
        return;
      }

      const settings = this.getCurrentSettings();
      await StorageService.savePreset(name, settings);
      await this.loadPresets();

      // Clear input and select the new preset
      setValue(ELEMENT_IDS.presetName, "");
      setValue(ELEMENT_IDS.presetSelect, name);
      setEnabled(ELEMENT_IDS.deletePresetBtn, true);

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Preset saved:`, name);
      }
    } catch (error) {
      handleStorageError(error, "Saving preset to storage");
    }
  }

  /**
   * Load a preset's settings
   */
  async loadPreset(name) {
    try {
      const preset = await StorageService.loadPreset(name);
      if (!preset) {
        console.warn(`${EXTENSION_CONFIG.logPrefix} Preset not found:`, name);
        return;
      }

      // Apply preset settings
      setValue(ELEMENT_IDS.quality, preset.quality);
      setValue(ELEMENT_IDS.customWidth, preset.customWidth || "");
      setValue(ELEMENT_IDS.customHeight, preset.customHeight || "");
      setValue(ELEMENT_IDS.bgColor, preset.bgColor);
      setChecked(ELEMENT_IDS.transparentBg, preset.transparentBg);
      setValue(ELEMENT_IDS.format, preset.format);

      // Update UI
      setEnabled(ELEMENT_IDS.bgColor, !preset.transparentBg);
      if (this.selectedSVGs.length > 0) {
        this.updateUI(true);
      }

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Preset loaded:`, name);
      }
    } catch (error) {
      handleStorageError(error, "Loading preset from storage");
    }
  }

  /**
   * Delete a preset
   */
  async deletePreset(name) {
    try {
      await StorageService.deletePreset(name);
      await this.loadPresets();

      // Reset preset selection
      setValue(ELEMENT_IDS.presetSelect, "");
      setEnabled(ELEMENT_IDS.deletePresetBtn, false);

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Preset deleted:`, name);
      }
    } catch (error) {
      handleStorageError(error, "Deleting preset from storage");
    }
  }

  /**
   * Apply user settings to the form
   */
  applyUserSettings() {
    setValue(ELEMENT_IDS.filename, this.userSettings.defaultFilename);
    setValue(ELEMENT_IDS.quality, this.userSettings.defaultQuality);
    setValue(ELEMENT_IDS.bgColor, this.userSettings.defaultBackgroundColor);
    setChecked(ELEMENT_IDS.transparentBg, this.userSettings.defaultTransparent);
    setValue(ELEMENT_IDS.format, this.userSettings.defaultFormat);
  }
}
