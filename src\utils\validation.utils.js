/**
 * Validation Utility Functions
 * Centralizes input validation logic
 */

import {
  VALIDATION,
  SUPPORTED_FORMATS,
  ERROR_MESSAGES,
} from "../config/constants.js";

/**
 * Validation result structure
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - Whether the input is valid
 * @property {string} error - Error message if invalid
 */

/**
 * Create a validation result object
 * @param {boolean} valid - Whether validation passed
 * @param {string|null} error - Error message if validation failed
 * @returns {ValidationResult} Validation result object
 * @private
 */
function _createValidationResult(valid, error = null) {
  return {
    valid,
    error: valid ? null : error,
  };
}

/**
 * Validate filename input
 * @param {string} filename - Filename to validate
 * @returns {ValidationResult} Validation result
 */
export function validateFilename(filename) {
  if (!filename || typeof filename !== "string") {
    return _createValidationResult(false, ERROR_MESSAGES.invalidFilename);
  }

  const trimmed = filename.trim();

  if (trimmed.length < VALIDATION.filename.minLength) {
    return _createValidationResult(
      false,
      `Filename must be at least ${VALIDATION.filename.minLength} character long`
    );
  }

  if (trimmed.length > VALIDATION.filename.maxLength) {
    return _createValidationResult(
      false,
      `Filename must be less than ${VALIDATION.filename.maxLength} characters`
    );
  }

  if (VALIDATION.filename.invalidChars.test(trimmed)) {
    return _createValidationResult(
      false,
      'Filename contains invalid characters: < > : " / \\ | ? *'
    );
  }

  return _createValidationResult(true);
}

/**
 * Validate custom dimensions
 * @param {string|number} width - Width value
 * @param {string|number} height - Height value
 * @returns {ValidationResult} Validation result
 */
export function validateDimensions(width, height) {
  const w = parseInt(width);
  const h = parseInt(height);

  // Both must be provided if either is provided
  if ((width && !height) || (!width && height)) {
    return {
      valid: false,
      error: "Both width and height must be provided for custom dimensions",
    };
  }

  // If neither provided, it's valid (will use default scaling)
  if (!width && !height) {
    return {
      valid: true,
      error: null,
    };
  }

  if (isNaN(w) || isNaN(h)) {
    return {
      valid: false,
      error: "Width and height must be valid numbers",
    };
  }

  if (w < VALIDATION.dimensions.min || h < VALIDATION.dimensions.min) {
    return {
      valid: false,
      error: `Dimensions must be at least ${VALIDATION.dimensions.min}px`,
    };
  }

  if (w > VALIDATION.dimensions.max || h > VALIDATION.dimensions.max) {
    return {
      valid: false,
      error: `Dimensions must be less than ${VALIDATION.dimensions.max}px`,
    };
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Validate quality scale value
 * @param {string|number} quality - Quality scale value
 * @returns {ValidationResult} Validation result
 */
export function validateQuality(quality) {
  const q = parseInt(quality);

  if (isNaN(q)) {
    return {
      valid: false,
      error: "Quality must be a valid number",
    };
  }

  if (q < VALIDATION.quality.min || q > VALIDATION.quality.max) {
    return {
      valid: false,
      error: `Quality must be between ${VALIDATION.quality.min} and ${VALIDATION.quality.max}`,
    };
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Validate file format
 * @param {string} format - File format
 * @returns {ValidationResult} Validation result
 */
export function validateFormat(format) {
  const validFormats = Object.values(SUPPORTED_FORMATS).map((f) => f.value);

  if (!validFormats.includes(format)) {
    return {
      valid: false,
      error: `Unsupported format. Valid formats: ${validFormats.join(", ")}`,
    };
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Validate color value (hex color)
 * @param {string} color - Color value
 * @returns {ValidationResult} Validation result
 */
export function validateColor(color) {
  if (!color || typeof color !== "string") {
    return {
      valid: false,
      error: "Color must be provided",
    };
  }

  const hexColorRegex = /^#[0-9A-Fa-f]{6}$/;

  if (!hexColorRegex.test(color)) {
    return {
      valid: false,
      error: "Color must be a valid hex color (e.g., #FF0000)",
    };
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Validate preset name
 * @param {string} name - Preset name
 * @param {string[]} existingNames - Array of existing preset names
 * @returns {ValidationResult} Validation result
 */
export function validatePresetName(name, existingNames = []) {
  if (!name || typeof name !== "string") {
    return {
      valid: false,
      error: ERROR_MESSAGES.presetNameRequired,
    };
  }

  const trimmed = name.trim();

  if (trimmed.length === 0) {
    return {
      valid: false,
      error: ERROR_MESSAGES.presetNameRequired,
    };
  }

  if (trimmed.length > 50) {
    return {
      valid: false,
      error: "Preset name must be less than 50 characters",
    };
  }

  if (existingNames.includes(trimmed)) {
    return {
      valid: false,
      error: ERROR_MESSAGES.presetExists,
    };
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Validate SVG data structure
 * @param {Object} svgData - SVG data object
 * @returns {ValidationResult} Validation result
 */
export function validateSVGData(svgData) {
  if (!svgData || typeof svgData !== "object") {
    return {
      valid: false,
      error: "Invalid SVG data structure",
    };
  }

  const requiredFields = ["svgString", "width", "height"];

  for (const field of requiredFields) {
    if (!(field in svgData)) {
      return {
        valid: false,
        error: `Missing required field: ${field}`,
      };
    }
  }

  if (typeof svgData.svgString !== "string" || svgData.svgString.length === 0) {
    return {
      valid: false,
      error: "SVG string must be a non-empty string",
    };
  }

  if (typeof svgData.width !== "number" || svgData.width <= 0) {
    return {
      valid: false,
      error: "SVG width must be a positive number",
    };
  }

  if (typeof svgData.height !== "number" || svgData.height <= 0) {
    return {
      valid: false,
      error: "SVG height must be a positive number",
    };
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Validate conversion settings object
 * @param {Object} settings - Conversion settings
 * @returns {ValidationResult} Validation result
 */
export function validateConversionSettings(settings) {
  if (!settings || typeof settings !== "object") {
    return {
      valid: false,
      error: "Invalid settings object",
    };
  }

  // Validate quality if provided
  if (settings.quality !== undefined) {
    const qualityValidation = validateQuality(settings.quality);
    if (!qualityValidation.valid) {
      return qualityValidation;
    }
  }

  // Validate dimensions if provided
  if (
    settings.customWidth !== undefined ||
    settings.customHeight !== undefined
  ) {
    const dimensionsValidation = validateDimensions(
      settings.customWidth,
      settings.customHeight
    );
    if (!dimensionsValidation.valid) {
      return dimensionsValidation;
    }
  }

  // Validate format if provided
  if (settings.format !== undefined) {
    const formatValidation = validateFormat(settings.format);
    if (!formatValidation.valid) {
      return formatValidation;
    }
  }

  // Validate background color if provided and not transparent
  if (settings.bgColor !== undefined && !settings.transparentBg) {
    const colorValidation = validateColor(settings.bgColor);
    if (!colorValidation.valid) {
      return colorValidation;
    }
  }

  return {
    valid: true,
    error: null,
  };
}

/**
 * Sanitize filename by removing invalid characters
 * @param {string} filename - Original filename
 * @returns {string} Sanitized filename
 */
export function sanitizeFilename(filename) {
  if (!filename || typeof filename !== "string") {
    return "image";
  }

  return filename
    .trim()
    .replace(VALIDATION.filename.invalidChars, "_")
    .substring(0, VALIDATION.filename.maxLength);
}

/**
 * Normalize and validate settings object
 * @param {Object} settings - Raw settings object
 * @returns {Object} Normalized and validated settings
 */
export function normalizeSettings(settings) {
  const normalized = {
    quality: parseInt(settings.quality) || 1,
    customWidth: settings.customWidth ? parseInt(settings.customWidth) : null,
    customHeight: settings.customHeight
      ? parseInt(settings.customHeight)
      : null,
    bgColor: settings.bgColor || "#ffffff",
    transparentBg: Boolean(settings.transparentBg),
    format: settings.format || "png",
  };

  // Validate normalized settings
  const validation = validateConversionSettings(normalized);
  if (!validation.valid) {
    throw new Error(`Invalid settings: ${validation.error}`);
  }

  return normalized;
}
