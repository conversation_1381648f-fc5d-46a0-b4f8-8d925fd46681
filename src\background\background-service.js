/**
 * Background Service
 * Enhanced background script with proper message routing and task management
 */

import {
  MESSAGE_ACTIONS,
  EXTENSION_CONFIG,
  DEBUG,
} from "../config/constants.js";

/**
 * Background service for managing extension lifecycle and communication
 */
class BackgroundService {
  constructor() {
    this.messageHandlers = new Map();
    this.activeDownloads = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the background service
   */
  init() {
    if (this.isInitialized) return;

    this._setupMessageHandlers();
    this._attachEventListeners();
    this.isInitialized = true;

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Background service initialized`
      );
    }
  }

  /**
   * Set up message handlers
   * @private
   */
  _setupMessageHandlers() {
    this.messageHandlers.set(
      MESSAGE_ACTIONS.downloadFromToolbar,
      this._handleDownloadFromToolbar.bind(this)
    );

    // Add more handlers as needed
    this.messageHandlers.set(
      "openOptionsPage",
      this._handleOpenOptionsPage.bind(this)
    );
    this.messageHandlers.set(
      "getActiveTab",
      this._handleGetActiveTab.bind(this)
    );
  }

  /**
   * Attach event listeners
   * @private
   */
  _attachEventListeners() {
    // Message listener
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this._handleMessage(message, sender, sendResponse);
      return true; // Keep channel open for async responses
    });

    // Installation listener
    chrome.runtime.onInstalled.addListener((details) => {
      this._handleInstalled(details);
    });

    // Startup listener
    chrome.runtime.onStartup.addListener(() => {
      this._handleStartup();
    });

    // Tab update listener (optional - for future features)
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this._handleTabUpdated(tabId, changeInfo, tab);
    });
  }

  /**
   * Handle incoming messages
   * @param {Object} message - Message object
   * @param {Object} sender - Sender information
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleMessage(message, sender, sendResponse) {
    try {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Background received message:`,
          message
        );
      }

      const { action } = message;
      const handler = this.messageHandlers.get(action);

      if (handler) {
        await handler(message, sender, sendResponse);
      } else {
        if (DEBUG.enabled) {
          console.warn(
            `${EXTENSION_CONFIG.logPrefix} Unknown action in background:`,
            action
          );
        }
        sendResponse({
          success: false,
          error: `Unknown action: ${action}`,
        });
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Background message handler error:`,
        error
      );
      sendResponse({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Handle download from toolbar request
   * @param {Object} message - Message object
   * @param {Object} sender - Sender information
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleDownloadFromToolbar(message, sender, sendResponse) {
    try {
      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Opening popup for download`);
      }

      // Open the extension popup
      await chrome.action.openPopup();

      sendResponse({ success: true });
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error opening popup:`,
        error
      );

      // Fallback: try to open popup programmatically
      try {
        await chrome.action.setPopup({ popup: "popup.html" });
        sendResponse({ success: true });
      } catch (fallbackError) {
        console.error(
          `${EXTENSION_CONFIG.logPrefix} Fallback popup error:`,
          fallbackError
        );
        sendResponse({
          success: false,
          error: "Failed to open popup",
        });
      }
    }
  }

  /**
   * Handle open options page request
   * @param {Object} message - Message object
   * @param {Object} sender - Sender information
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleOpenOptionsPage(message, sender, sendResponse) {
    try {
      await chrome.runtime.openOptionsPage();
      sendResponse({ success: true });
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error opening options page:`,
        error
      );
      sendResponse({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Handle get active tab request
   * @param {Object} message - Message object
   * @param {Object} sender - Sender information
   * @param {Function} sendResponse - Response callback
   * @private
   */
  async _handleGetActiveTab(message, sender, sendResponse) {
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });

      sendResponse({
        success: true,
        tab: activeTab,
      });
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error getting active tab:`,
        error
      );
      sendResponse({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Handle extension installation
   * @param {Object} details - Installation details
   * @private
   */
  _handleInstalled(details) {
    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Extension installed:`,
        details
      );
    }

    switch (details.reason) {
      case "install":
        this._onFirstInstall();
        break;
      case "update":
        this._onUpdate(details.previousVersion);
        break;
      case "chrome_update":
      case "shared_module_update":
        // Handle browser/shared module updates if needed
        break;
    }
  }

  /**
   * Handle extension startup
   * @private
   */
  _handleStartup() {
    if (DEBUG.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} Extension startup`);
    }

    // Cleanup any stale state
    this.activeDownloads.clear();
  }

  /**
   * Handle tab updates
   * @param {number} tabId - Tab ID
   * @param {Object} changeInfo - Change information
   * @param {Object} tab - Tab object
   * @private
   */
  _handleTabUpdated(tabId, changeInfo, tab) {
    // Future: Could be used for automatic SVG detection or content script injection
    if (DEBUG.enabled && changeInfo.status === "complete") {
      console.log(`${EXTENSION_CONFIG.logPrefix} Tab updated:`, tabId, tab.url);
    }
  }

  /**
   * Handle first installation
   * @private
   */
  _onFirstInstall() {
    if (DEBUG.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} First installation`);
    }

    // Could open welcome page or set default settings
    // chrome.tabs.create({ url: 'welcome.html' });
  }

  /**
   * Handle extension update
   * @param {string} previousVersion - Previous version
   * @private
   */
  _onUpdate(previousVersion) {
    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Updated from version:`,
        previousVersion
      );
    }

    // Handle migration if needed
    // this._migrateSettings(previousVersion);
  }

  /**
   * Send message to content script
   * @param {number} tabId - Tab ID
   * @param {Object} message - Message to send
   * @returns {Promise<Object>} Response from content script
   */
  async sendMessageToContentScript(tabId, message) {
    try {
      const response = await chrome.tabs.sendMessage(tabId, message);
      return response;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error sending message to content script:`,
        error
      );
      throw error;
    }
  }

  /**
   * Clean up background service
   */
  cleanup() {
    this.messageHandlers.clear();
    this.activeDownloads.clear();
    this.isInitialized = false;
  }
}

// Initialize the background service
const backgroundService = new BackgroundService();
backgroundService.init();

// Export for potential external access (development)
if (DEBUG.enabled) {
  globalThis.backgroundService = backgroundService;
}
