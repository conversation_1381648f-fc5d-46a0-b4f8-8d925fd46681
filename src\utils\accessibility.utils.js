/**
 * SVG Switcher Extension - Accessibility Utilities
 * Enhances accessibility with ARIA support, keyboard navigation, and screen reader compatibility
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";

/**
 * Accessibility manager singleton
 */
class AccessibilityManager {
  constructor() {
    this.liveRegion = null;
    this.focusHistory = [];
    this.keyboardHandlers = new Map();
    this.initialized = false;
  }

  /**
   * Initialize accessibility features
   * @param {Document} document - Document to initialize accessibility for
   */
  initialize(document) {
    if (this.initialized) return;

    this.createLiveRegion(document);
    this.setupKeyboardNavigation(document);
    this.enhanceFormControls(document);
    this.setupFocusManagement(document);

    this.initialized = true;

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Accessibility features initialized`
      );
    }
  }

  /**
   * Create ARIA live region for announcements
   * @param {Document} document - Document to create live region in
   */
  createLiveRegion(document) {
    this.liveRegion = document.createElement("div");
    this.liveRegion.setAttribute("aria-live", "polite");
    this.liveRegion.setAttribute("aria-atomic", "true");
    this.liveRegion.className = "sr-only";
    this.liveRegion.style.cssText = `
      position: absolute !important;
      left: -10000px !important;
      top: auto !important;
      width: 1px !important;
      height: 1px !important;
      overflow: hidden !important;
    `;
    document.body.appendChild(this.liveRegion);
  }

  /**
   * Announce message to screen readers
   * @param {string} message - Message to announce
   * @param {string} priority - Announcement priority ('polite' or 'assertive')
   */
  announce(message, priority = "polite") {
    if (!this.liveRegion) return;

    this.liveRegion.setAttribute("aria-live", priority);

    // Clear and set new message
    this.liveRegion.textContent = "";
    setTimeout(() => {
      this.liveRegion.textContent = message;
    }, 100);

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Accessibility announcement: ${message}`
      );
    }
  }

  /**
   * Set up keyboard navigation
   * @param {Document} document - Document to set up navigation for
   */
  setupKeyboardNavigation(document) {
    // Escape key handler for clearing selections
    const escapeHandler = (event) => {
      if (event.key === "Escape") {
        this.handleEscapeKey(event);
      }
    };

    // Tab trap for modals/popups
    const tabHandler = (event) => {
      if (event.key === "Tab") {
        this.handleTabNavigation(event);
      }
    };

    // Arrow key navigation for custom controls
    const arrowHandler = (event) => {
      if (
        ["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(event.key)
      ) {
        this.handleArrowNavigation(event);
      }
    };

    document.addEventListener("keydown", escapeHandler);
    document.addEventListener("keydown", tabHandler);
    document.addEventListener("keydown", arrowHandler);

    this.keyboardHandlers.set("escape", escapeHandler);
    this.keyboardHandlers.set("tab", tabHandler);
    this.keyboardHandlers.set("arrow", arrowHandler);
  }

  /**
   * Handle escape key press
   * @param {KeyboardEvent} event - Keyboard event
   */
  handleEscapeKey(event) {
    // Check for open modals or selections to clear
    const modal = document.querySelector('[role="dialog"]:not([hidden])');
    if (modal) {
      this.closeModal(modal);
      event.preventDefault();
      return;
    }

    // Clear selections if any
    const selectedElements = document.querySelectorAll(".svg2png-selected");
    if (selectedElements.length > 0) {
      this.announce(`Cleared ${selectedElements.length} selections`);
      event.preventDefault();
    }
  }

  /**
   * Handle tab navigation with focus trapping
   * @param {KeyboardEvent} event - Keyboard event
   */
  handleTabNavigation(event) {
    const modal = event.target.closest('[role="dialog"]');
    if (!modal) return;

    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey && document.activeElement === firstElement) {
      lastElement.focus();
      event.preventDefault();
    } else if (!event.shiftKey && document.activeElement === lastElement) {
      firstElement.focus();
      event.preventDefault();
    }
  }

  /**
   * Handle arrow key navigation for custom controls
   * @param {KeyboardEvent} event - Keyboard event
   */
  handleArrowNavigation(event) {
    const target = event.target;

    // Handle radio button groups
    if (target.type === "radio") {
      this.handleRadioNavigation(event);
      return;
    }

    // Handle custom select-like controls
    if (target.getAttribute("role") === "listbox") {
      this.handleListboxNavigation(event);
      return;
    }
  }

  /**
   * Handle radio button navigation
   * @param {KeyboardEvent} event - Keyboard event
   */
  handleRadioNavigation(event) {
    const radioGroup = document.querySelectorAll(
      `input[name="${event.target.name}"]`
    );
    const currentIndex = Array.from(radioGroup).indexOf(event.target);
    let nextIndex;

    if (event.key === "ArrowDown" || event.key === "ArrowRight") {
      nextIndex = (currentIndex + 1) % radioGroup.length;
    } else if (event.key === "ArrowUp" || event.key === "ArrowLeft") {
      nextIndex = (currentIndex - 1 + radioGroup.length) % radioGroup.length;
    }

    if (nextIndex !== undefined) {
      radioGroup[nextIndex].focus();
      radioGroup[nextIndex].checked = true;
      radioGroup[nextIndex].dispatchEvent(
        new Event("change", { bubbles: true })
      );
      event.preventDefault();
    }
  }

  /**
   * Enhance form controls with accessibility attributes
   * @param {Document} document - Document to enhance
   */
  enhanceFormControls(document) {
    // Enhance input labels
    const inputs = document.querySelectorAll("input, select, textarea");
    inputs.forEach((input) => {
      this.enhanceFormControl(input);
    });

    // Enhance buttons
    const buttons = document.querySelectorAll("button");
    buttons.forEach((button) => {
      this.enhanceButton(button);
    });

    // Enhance progress bars
    const progressBars = document.querySelectorAll(
      '[role="progressbar"], .progress-bar'
    );
    progressBars.forEach((progressBar) => {
      this.enhanceProgressBar(progressBar);
    });
  }

  /**
   * Enhance individual form control
   * @param {Element} control - Form control to enhance
   */
  enhanceFormControl(control) {
    // Add required field indicators
    if (
      control.hasAttribute("required") &&
      !control.getAttribute("aria-label")
    ) {
      const label = document.querySelector(`label[for="${control.id}"]`);
      if (label && !label.textContent.includes("required")) {
        control.setAttribute("aria-describedby", `${control.id}-required`);

        const requiredSpan = document.createElement("span");
        requiredSpan.id = `${control.id}-required`;
        requiredSpan.className = "sr-only";
        requiredSpan.textContent = "required";
        label.appendChild(requiredSpan);
      }
    }

    // Add error handling
    if (control.getAttribute("aria-invalid") === "true") {
      const errorId = `${control.id}-error`;
      if (!document.getElementById(errorId)) {
        const errorMsg = document.createElement("div");
        errorMsg.id = errorId;
        errorMsg.className = "error-message";
        errorMsg.setAttribute("role", "alert");
        control.parentNode.insertBefore(errorMsg, control.nextSibling);

        const describedBy = control.getAttribute("aria-describedby") || "";
        control.setAttribute(
          "aria-describedby",
          `${describedBy} ${errorId}`.trim()
        );
      }
    }
  }

  /**
   * Enhance button accessibility
   * @param {Element} button - Button to enhance
   */
  enhanceButton(button) {
    // Ensure buttons have accessible names
    if (!button.getAttribute("aria-label") && !button.textContent.trim()) {
      console.warn(
        `${EXTENSION_CONFIG.logPrefix} Button missing accessible name:`,
        button
      );
    }

    // Add loading state support
    if (button.hasAttribute("data-loading")) {
      button.setAttribute("aria-busy", "true");
      button.setAttribute("aria-describedby", `${button.id}-loading`);
    }
  }

  /**
   * Enhance progress bar accessibility
   * @param {Element} progressBar - Progress bar to enhance
   */
  enhanceProgressBar(progressBar) {
    if (!progressBar.hasAttribute("role")) {
      progressBar.setAttribute("role", "progressbar");
    }

    if (!progressBar.hasAttribute("aria-valuemin")) {
      progressBar.setAttribute("aria-valuemin", "0");
    }

    if (!progressBar.hasAttribute("aria-valuemax")) {
      progressBar.setAttribute("aria-valuemax", "100");
    }

    // Add live updates for progress changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "aria-valuenow"
        ) {
          const value = progressBar.getAttribute("aria-valuenow");
          if (value) {
            this.announce(`Progress: ${value}%`);
          }
        }
      });
    });

    observer.observe(progressBar, {
      attributes: true,
      attributeFilter: ["aria-valuenow"],
    });
  }

  /**
   * Set up focus management
   * @param {Document} document - Document to set up focus management for
   */
  setupFocusManagement(document) {
    // Track focus history for restoration
    document.addEventListener("focusin", (event) => {
      this.focusHistory.push(event.target);
      if (this.focusHistory.length > 10) {
        this.focusHistory.shift();
      }
    });

    // Auto-focus management for dynamic content
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.handleNewContent(node);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  /**
   * Handle new content for accessibility
   * @param {Element} element - New element added to DOM
   */
  handleNewContent(element) {
    // Auto-focus first focusable element in modals
    if (element.getAttribute("role") === "dialog") {
      const firstFocusable = element.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      if (firstFocusable) {
        setTimeout(() => firstFocusable.focus(), 100);
      }
    }

    // Enhance any new form controls
    const newControls = element.querySelectorAll(
      "input, select, textarea, button"
    );
    newControls.forEach((control) => {
      if (control.tagName === "BUTTON") {
        this.enhanceButton(control);
      } else {
        this.enhanceFormControl(control);
      }
    });
  }

  /**
   * Update progress bar value accessibly
   * @param {Element} progressBar - Progress bar element
   * @param {number} value - New progress value (0-100)
   * @param {string} label - Optional label for the progress
   */
  updateProgress(progressBar, value, label) {
    progressBar.setAttribute("aria-valuenow", value.toString());

    if (label) {
      progressBar.setAttribute("aria-label", label);
    }

    // Announce significant progress milestones
    if (value === 0) {
      this.announce("Process started");
    } else if (value === 100) {
      this.announce("Process completed");
    } else if (value % 25 === 0) {
      this.announce(`${value}% complete`);
    }
  }

  /**
   * Clean up accessibility features
   */
  cleanup() {
    if (this.liveRegion) {
      this.liveRegion.remove();
      this.liveRegion = null;
    }

    this.keyboardHandlers.forEach((handler, key) => {
      document.removeEventListener("keydown", handler);
    });
    this.keyboardHandlers.clear();

    this.focusHistory = [];
    this.initialized = false;
  }
}

// Create singleton instance
const accessibilityManager = new AccessibilityManager();

/**
 * Initialize accessibility features
 * @param {Document} document - Document to initialize
 */
export function initializeAccessibility(document) {
  accessibilityManager.initialize(document);
}

/**
 * Announce message to screen readers
 * @param {string} message - Message to announce
 * @param {string} priority - Priority level
 */
export function announceToScreenReader(message, priority = "polite") {
  accessibilityManager.announce(message, priority);
}

/**
 * Update progress accessibly
 * @param {Element} progressBar - Progress bar element
 * @param {number} value - Progress value
 * @param {string} label - Optional label
 */
export function updateProgressAccessibly(progressBar, value, label) {
  accessibilityManager.updateProgress(progressBar, value, label);
}

/**
 * Clean up accessibility features
 */
export function cleanupAccessibility() {
  accessibilityManager.cleanup();
}
