/**
 * SVG Utility Functions
 * Handles SVG processing, cleaning, and preparation for conversion
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";

/**
 * Clean and normalize SVG string for conversion
 * @param {string} svgString - Raw SVG string
 * @returns {string} Cleaned SVG string
 */
export function cleanSVGString(svgString) {
  if (!svgString || typeof svgString !== "string") {
    throw new Error("Invalid SVG string provided");
  }

  // Define cleaning operations for better maintainability
  const cleaningOperations = [
    // Remove XML declarations and DOCTYPE
    { pattern: /<\?xml[^>]*\?>/gi, replacement: "" },
    { pattern: /<!DOCTYPE[^>]*>/gi, replacement: "" },
    // Remove comments
    { pattern: /<!--[\s\S]*?-->/g, replacement: "" },
    // Remove script tags for security
    {
      pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      replacement: "",
    },
    // Remove event handlers for security
    { pattern: /\s*on\w+\s*=\s*["'][^"']*["']/gi, replacement: "" },
    // Normalize whitespace
    { pattern: /\s+/g, replacement: " " },
  ];

  // Apply all cleaning operations
  let cleanedSVG = svgString;
  cleaningOperations.forEach((operation) => {
    cleanedSVG = cleanedSVG.replace(operation.pattern, operation.replacement);
  });

  cleanedSVG = cleanedSVG.trim();

  // Ensure SVG has proper namespace
  if (!cleanedSVG.includes('xmlns="http://www.w3.org/2000/svg"')) {
    cleanedSVG = cleanedSVG.replace(
      /<svg([^>]*)>/i,
      '<svg$1 xmlns="http://www.w3.org/2000/svg">'
    );
  }

  return cleanedSVG;
}

/**
 * Process SVG string for conversion (add size constraints, fix issues)
 * @param {string} svgString - SVG string to process
 * @param {Object} options - Processing options
 * @returns {string} Processed SVG string
 */
export function processSVGForConversion(svgString, options = {}) {
  let processedSVG = cleanSVGString(svgString);

  // Fix common SVG issues that prevent proper rendering
  processedSVG = processedSVG
    // Ensure proper closing tags
    .replace(/<(\w+)([^>]*?)\/>/g, "<$1$2></$1>")

    // Fix self-closing tags that should not be self-closing
    .replace(
      /<(path|circle|ellipse|line|rect|polygon|polyline)([^>]*?)><\/\1>/gi,
      "<$1$2/>"
    )

    // Remove empty elements that might cause issues
    .replace(/<g[^>]*>\s*<\/g>/g, "")
    .replace(/<defs[^>]*>\s*<\/defs>/g, "")

    // Ensure proper attribute quoting
    .replace(/(\w+)=([^"'\s>]+)/g, '$1="$2"');

  // Add viewBox if missing and we have width/height
  if (!processedSVG.includes("viewBox") && options.width && options.height) {
    processedSVG = processedSVG.replace(
      /<svg([^>]*)>/i,
      `<svg$1 viewBox="0 0 ${options.width} ${options.height}">`
    );
  }

  if (DEBUG.enabled && DEBUG.logLevel === "debug") {
    console.log(`${EXTENSION_CONFIG.logPrefix} Processed SVG for conversion`);
  }

  return processedSVG;
}

/**
 * Extract SVG dimensions from element or viewBox
 * @param {SVGElement} svgElement - SVG DOM element
 * @returns {Object} Dimensions object with width and height
 */
export function extractSVGDimensions(svgElement) {
  let width, height;

  // Try to get dimensions from viewBox first
  const viewBox = svgElement.getAttribute("viewBox");
  if (viewBox) {
    const [, , vbWidth, vbHeight] = viewBox.split(/\s+/).map(parseFloat);
    if (vbWidth && vbHeight && !isNaN(vbWidth) && !isNaN(vbHeight)) {
      width = vbWidth;
      height = vbHeight;
    }
  }

  // If no viewBox or invalid, try width/height attributes
  if (!width || !height) {
    const widthAttr = svgElement.getAttribute("width");
    const heightAttr = svgElement.getAttribute("height");

    if (widthAttr && heightAttr) {
      width = parseFloat(widthAttr.replace(/[^\d.]/g, ""));
      height = parseFloat(heightAttr.replace(/[^\d.]/g, ""));
    }
  }

  // If still no dimensions, use bounding box
  if (!width || !height || isNaN(width) || isNaN(height)) {
    try {
      const bbox = svgElement.getBBox();
      width = bbox.width;
      height = bbox.height;
    } catch (error) {
      // Fallback to computed style
      const computedStyle = window.getComputedStyle(svgElement);
      width = parseFloat(computedStyle.width) || 100;
      height = parseFloat(computedStyle.height) || 100;
    }
  }

  // Final fallback to default dimensions
  if (!width || !height || isNaN(width) || isNaN(height)) {
    width = 100;
    height = 100;
  }

  return {
    width: Math.max(1, Math.round(width)),
    height: Math.max(1, Math.round(height)),
  };
}

/**
 * Create SVG data object from DOM element
 * @param {SVGElement} svgElement - SVG DOM element
 * @returns {Object} SVG data object
 */
export function createSVGDataFromElement(svgElement) {
  try {
    // Clone the SVG to avoid modifying the original
    const svgClone = svgElement.cloneNode(true);

    // Remove any selection styling classes
    svgClone.classList.remove("svg2png-selected");
    svgClone.querySelectorAll(".svg2png-selected").forEach((el) => {
      el.classList.remove("svg2png-selected");
    });

    // Extract dimensions
    const dimensions = extractSVGDimensions(svgClone);

    // Get the SVG string
    const svgString = new XMLSerializer().serializeToString(svgClone);

    // Get additional metadata
    const rect = svgElement.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(svgElement);

    return {
      svgString: svgString,
      width: dimensions.width,
      height: dimensions.height,
      originalWidth: dimensions.width,
      originalHeight: dimensions.height,
      elementRect: {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
      },
      computedStyle: {
        display: computedStyle.display,
        visibility: computedStyle.visibility,
      },
      timestamp: Date.now(),
    };
  } catch (error) {
    console.error(
      `${EXTENSION_CONFIG.logPrefix} Error creating SVG data:`,
      error
    );
    throw new Error(`Failed to process SVG element: ${error.message}`);
  }
}

/**
 * Calculate output dimensions based on settings
 * @param {Object} svgData - SVG data object
 * @param {Object} settings - Conversion settings
 * @returns {Object} Output dimensions
 */
export function calculateOutputDimensions(svgData, settings) {
  const { width: originalWidth, height: originalHeight } = svgData;
  let outputWidth, outputHeight;

  // Use custom dimensions if provided
  if (settings.customWidth && settings.customHeight) {
    outputWidth = parseInt(settings.customWidth);
    outputHeight = parseInt(settings.customHeight);
  }
  // Use quality scaling
  else {
    const qualityScale = parseInt(settings.quality) || 1;
    outputWidth = Math.round(originalWidth * qualityScale);
    outputHeight = Math.round(originalHeight * qualityScale);
  }

  // Ensure minimum dimensions
  outputWidth = Math.max(1, outputWidth);
  outputHeight = Math.max(1, outputHeight);

  return {
    width: outputWidth,
    height: outputHeight,
    scale: outputWidth / originalWidth,
  };
}

/**
 * Create a data URL from SVG string
 * @param {string} svgString - SVG string
 * @returns {string} Data URL
 */
export function createSVGDataURL(svgString) {
  const cleanedSVG = cleanSVGString(svgString);
  return "data:image/svg+xml;charset=utf-8," + encodeURIComponent(cleanedSVG);
}

/**
 * Check if element is an SVG or contains SVG
 * @param {Element} element - DOM element to check
 * @returns {SVGElement|null} SVG element if found, null otherwise
 */
export function findSVGElement(element) {
  if (!element) return null;

  // Check if element itself is SVG
  if (element.tagName && element.tagName.toLowerCase() === "svg") {
    return element;
  }

  // Check if element is inside an SVG
  if (element.closest) {
    return element.closest("svg");
  }

  return null;
}

/**
 * Optimize SVG for better conversion results
 * @param {string} svgString - SVG string to optimize
 * @returns {string} Optimized SVG string
 */
export function optimizeSVGForConversion(svgString) {
  if (!svgString || typeof svgString !== "string") {
    throw new Error("Invalid SVG string provided for optimization");
  }

  let optimizedSVG = cleanSVGString(svgString);

  // Apply optimization transformations
  const optimizations = [
    // Convert relative units to absolute pixels where possible
    {
      pattern: /(\d+(?:\.\d+)?)em/g,
      replacement: (match, value) => `${parseFloat(value) * 16}px`,
    },

    // Simplify redundant transformations
    {
      pattern: /transform\s*=\s*["']translate\(0,?\s*0\)["']/g,
      replacement: "",
    },

    // Remove unnecessary precision in numbers
    {
      pattern: /(\d+)\.(\d{3,})/g,
      replacement: (match, integer, decimal) => {
        const rounded = parseFloat(`${integer}.${decimal}`).toFixed(2);
        return parseFloat(rounded).toString();
      },
    },

    // Consolidate multiple spaces in path data
    {
      pattern: /\s{2,}/g,
      replacement: " ",
    },

    // Remove trailing zeros in decimal numbers
    {
      pattern: /(\d+)\.?0+(?=\D)/g,
      replacement: "$1",
    },
  ];

  // Apply all optimizations
  optimizations.forEach(({ pattern, replacement }) => {
    if (typeof replacement === "function") {
      optimizedSVG = optimizedSVG.replace(pattern, replacement);
    } else {
      optimizedSVG = optimizedSVG.replace(pattern, replacement);
    }
  });

  // Ensure proper structure
  optimizedSVG = processSVGForConversion(optimizedSVG);

  if (DEBUG.enabled && DEBUG.logLevel === "debug") {
    const originalSize = svgString.length;
    const optimizedSize = optimizedSVG.length;
    const savings = (
      ((originalSize - optimizedSize) / originalSize) *
      100
    ).toFixed(1);
    console.log(
      `${EXTENSION_CONFIG.logPrefix} SVG optimized: ${savings}% size reduction`
    );
  }

  return optimizedSVG;
}

/**
 * Validate SVG string structure
 * @param {string} svgString - SVG string to validate
 * @returns {boolean} Whether SVG string is valid
 */
export function isValidSVG(svgString) {
  if (!svgString || typeof svgString !== "string") {
    return false;
  }

  try {
    // Basic SVG structure validation
    const parser = new DOMParser();
    const doc = parser.parseFromString(svgString, "image/svg+xml");

    // Check for parsing errors
    const errorNode = doc.querySelector("parsererror");
    if (errorNode) {
      return false;
    }

    // Check if it has an SVG root element
    const svgElement = doc.querySelector("svg");
    return svgElement !== null;
  } catch (error) {
    return false;
  }
}

/**
 * Get MIME type for conversion format
 * @param {string} format - Output format
 * @returns {string} MIME type
 */
export function getMimeType(format) {
  const mimeTypes = {
    png: "image/png",
    jpeg: "image/jpeg",
    jpg: "image/jpeg",
    webp: "image/webp",
  };

  return mimeTypes[format.toLowerCase()] || "image/png";
}
