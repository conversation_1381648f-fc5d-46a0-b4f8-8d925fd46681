# Complete Changelog

## v2.0.0

**June 19, 2025** • _Stable Release_

🎉 **Major Features**

- **Multi-format export**: Convert SVG to PNG, JPEG, WebP, or keep as original SVG
- **Batch processing with ZIP downloads**: Process multiple SVGs and download as compressed archive
- **Clipboard integration**: Copy converted images directly to clipboard for quick sharing
- **Advanced quality scaling**: Choose from 1x to 4x resolution multipliers for crisp output
- **Floating toolbar**: Draggable, customizable toolbar with smart positioning and auto-hide options

🚀 **Enhancements**

- **Full accessibility support**: ARIA labels, keyboard navigation, and screen reader compatibility
- **Performance monitoring**: Built-in performance tracking and memory usage optimization
- **Settings persistence**: Save and load custom conversion presets for workflow efficiency
- **Background options**: Toggle transparent backgrounds or set custom background colors
- **Smart error handling**: Comprehensive error recovery with user-friendly feedback messages

🛠️ **Technical Improvements**

- Modular architecture with service-oriented design
- Enhanced memory management with automatic cleanup
- Optimized canvas rendering for better performance
- Context invalidation handling for extension stability

---

## v1.5.2

**June 18, 2025** • _Patch Release_

🐛 **Bug Fixes**

- Fixed feature suggestion and bug report links in settings page
- Updated developer branding and user count displays
- Improved SVG icon rendering in settings interface
- Corrected extension name references throughout UI

🔧 **Minor Improvements**

- Enhanced community statistics display
- Updated social media links and contact information
- Improved visual aesthetics of settings cards

---

## v1.5.0

**June 15, 2025** • _Feature Release_

✨ **New Features**

- **Preset management**: Save, load, and delete custom conversion settings
- **Progress tracking**: Real-time progress bars for batch conversions
- **Preview functionality**: Live preview of conversion results before download
- **Toolbar customization**: Remember toolbar position and behavior preferences

🚀 **Improvements**

- Enhanced file naming with automatic index handling for duplicates
- Improved SVG parsing for complex graphics and embedded styles
- Better validation for user inputs and file constraints
- Optimized conversion pipeline for faster processing

🔧 **Technical**

- Added lazy loading for improved startup performance
- Implemented robust state management system
- Enhanced error logging and debugging capabilities
- Updated dependencies for security and compatibility

---

## v1.0.0

**May 30, 2025** • _Initial Stable Release_

🎉 **Core Features**

- **SVG to image conversion**: High-quality conversion with canvas-based rendering
- **Multiple export formats**: Support for PNG, JPEG, and WebP output
- **Quality settings**: Adjustable resolution scaling for different use cases
- **Simple user interface**: Clean, intuitive popup design
- **File downloads**: Direct browser download integration

🛠️ **Architecture**

- Content script injection for SVG detection and selection
- Background service worker for download management
- Modular utility system for SVG processing and validation
- CSS-in-JS styling system for consistent theming

🔒 **Permissions**

- Active tab access for SVG detection
- Downloads permission for file saving
- Storage access for settings persistence
- Clipboard write access for copy functionality

---

## v0.9.5

**May 25, 2025** • _Release Candidate_

🐛 **Bug Fixes**

- Resolved conversion issues with complex SVG files containing gradients and filters
- Fixed memory leaks during batch processing of large file sets
- Corrected progress bar accuracy calculations
- Improved handling of SVG files with embedded fonts

🔧 **Technical Improvements**

- Updated core dependencies to latest stable versions
- Enhanced code documentation with JSDoc annotations
- Improved error logging with detailed stack traces
- Added performance benchmarking utilities

🚀 **Performance**

- Optimized canvas rendering for faster conversion times
- Reduced memory footprint during large batch operations
- Improved garbage collection handling

---

## v0.9.0

**May 15, 2025** • _Alpha Release_

✨ **Initial Implementation**

- Basic SVG to PNG conversion functionality
- Simple file selection and download mechanism
- Essential UI components and user interaction
- Core extension manifest and permissions setup

🛠️ **Foundation**

- Established modular code architecture
- Basic error handling and validation
- Simple settings and configuration system
- Initial content script and popup integration

---

_For technical support or feature requests, please visit our [GitHub repository](https://github.com/user/svg-switcher) or contact us through the extension settings page._
