/**
 * Settings Page Controller
 * Manages all settings page functionality and data persistence
 */

import { StorageService } from "../src/services/storage.service.js";
import {
  EXTENSION_CONFIG,
  DEBUG,
  DEFAULT_SETTINGS,
} from "../src/config/constants.js";
import {
  initializeAccessibility,
  announceToScreenReader,
} from "../src/utils/accessibility.utils.js";

/**
 * Settings page controller class - Enhanced with better organization
 */
class SettingsController {
  constructor() {
    this.currentSection = "general";
    this.settings = {};
    this.dimensionPresets = [];
    this.isLoading = false;
    this.autoSaveTimeout = null;
    this.eventHandlers = {}; // Store event handlers for easy cleanup
  }

  /**
   * Initialize the settings page
   */
  async init() {
    try {
      this.logDebug("Settings page initializing...");

      // Initialize accessibility
      initializeAccessibility(document);

      // Load current settings
      await this.loadSettings();

      // Set up event listeners and UI
      this.setupAllEventListeners();
      this.initializeUI();

      // Load presets
      await this.loadPresets();

      this.logDebug("Settings page initialized");
    } catch (error) {
      this.handleError("Settings initialization error:", error);
      this.showStatus("Failed to initialize settings page", "error");
    }
  }

  /**
   * Load settings from storage
   */
  async loadSettings() {
    try {
      const stored = await StorageService.get(["settings"], "sync");
      this.settings = { ...DEFAULT_SETTINGS, ...stored.settings };
      this.populateForm();
    } catch (error) {
      this.handleError("Error loading settings:", error);
      this.settings = DEFAULT_SETTINGS;
      this.populateForm();
    }
  }

  /**
   * Get default settings object
   */
  getDefaultSettings() {
    return {
      // General
      defaultFilename: "image",
      filenameCase: "original",
      replaceSpaces: false,
      appendTimestamp: false,
      defaultQuality: "4",
      maxDimension: 8192,

      // Export
      defaultFormat: "png",
      jpegQuality: 90,
      webpQuality: 90,
      defaultTransparent: false,
      defaultBackgroundColor: "#ffffff",
      preserveAspectRatio: true,

      // Behavior
      showToolbarOnSelection: true,
      autoHideToolbar: false,
      toolbarPosition: "top-right",
      rememberToolbarPosition: false,
      selectionModifier: "ctrl",
      clearOnEscape: true,
      visualFeedback: true,
      defaultDownloadType: "separate",
      showPreview: true,
      confirmBeforeDownload: false,

      // Advanced
      maxConcurrentConversions: 3,
      conversionTimeout: 30,
      enableCaching: true,
      cacheSize: 50,
      enableDebugMode: false,
      showPerformanceMetrics: false,

      // Accessibility
      enableScreenReaderSupport: true,
      highContrastMode: false,
      reduceMotion: false,
    };
  }

  /**
   * Set up all event listeners in a centralized way
   */
  setupAllEventListeners() {
    // Group all event listener setups
    const setups = [
      {
        name: "navigation",
        fn: this.setupNavigationListeners.bind(this),
      },
      {
        name: "buttons",
        fn: this.setupButtonListeners.bind(this),
      },
      {
        name: "rangeInputs",
        fn: this.setupRangeInputs.bind(this),
      },
      {
        name: "formChanges",
        fn: this.setupFormChangeListeners.bind(this),
      },
      {
        name: "presets",
        fn: this.setupPresetEventListeners.bind(this),
      },
      {
        name: "dimensionPresets",
        fn: this.setupDimensionPresetListeners.bind(this),
      },
      {
        name: "backupRestore",
        fn: this.setupBackupRestoreListeners.bind(this),
      },
      {
        name: "modal",
        fn: this.setupModal.bind(this),
      },
      {
        name: "fab",
        fn: this.setupFloatingActionButton.bind(this),
      },
    ];

    // Execute each setup function and track for potential cleanup
    setups.forEach((setup) => {
      this.eventHandlers[setup.name] = setup.fn() || [];
    });
  }
  /**
   * Set up navigation event listeners
   */
  setupNavigationListeners() {
    const handlers = [];
    document.querySelectorAll(".nav-link").forEach((link) => {
      const handler = (e) => {
        const section = link.getAttribute("data-section");
        // Only handle navigation links with data-section attribute
        if (section) {
          e.preventDefault();
          e.stopPropagation(); // Prevent other handlers from running
          this.showSection(section);
        }
      };

      link.addEventListener("click", handler);
      handlers.push({ element: link, event: "click", handler });
    });
    return handlers;
  }

  /**
   * Set up main button listeners
   */
  setupButtonListeners() {
    const handlers = [];

    // Save settings
    const saveBtn = document.getElementById("saveSettingsBtn");
    const saveHandler = () => this.saveAllSettings();
    saveBtn.addEventListener("click", saveHandler);
    handlers.push({ element: saveBtn, event: "click", handler: saveHandler });

    // Reset all
    const resetBtn = document.getElementById("resetAllBtn");
    const resetHandler = () => {
      this.confirmAction("Reset all settings to defaults?", () => {
        this.resetAllSettings();
      });
    };
    resetBtn.addEventListener("click", resetHandler);
    handlers.push({ element: resetBtn, event: "click", handler: resetHandler });

    return handlers;
  }

  /**
   * Set up range input value displays
   */
  setupRangeInputs() {
    // Define range inputs with their value displays
    const ranges = [
      { input: "jpegQuality", display: "jpegQualityValue" },
      { input: "webpQuality", display: "webpQualityValue" },
    ];

    const handlers = [];

    ranges.forEach((range) => {
      const input = document.getElementById(range.input);
      const display = document.getElementById(range.display);

      if (!input || !display) return;

      const handler = () => {
        display.textContent = `${input.value}%`;
      };

      input.addEventListener("input", handler);
      handlers.push({ element: input, event: "input", handler });
    });

    return handlers;
  }

  /**
   * Set up form change listeners
   */
  setupFormChangeListeners() {
    // Auto-save on changes
    const formElements = document.querySelectorAll(
      '.form-control, input[type="checkbox"], input[type="color"]'
    );

    formElements.forEach((element) => {
      const eventType = element.type === "range" ? "input" : "change";
      element.addEventListener(eventType, () => {
        // Debounce auto-save
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = setTimeout(() => {
          this.saveSettingsFromForm();
        }, 1000);
      });
    });
  }

  /**
   * Set up preset event listeners
   */
  setupPresetEventListeners() {
    document.getElementById("createPresetBtn").addEventListener("click", () => {
      this.createPreset();
    });
  }

  /**
   * Set up dimension preset listeners
   */
  setupDimensionPresetListeners() {
    document
      .getElementById("addDimensionPreset")
      .addEventListener("click", () => {
        this.addDimensionPreset();
      });
  }

  /**
   * Set up backup and restore listeners
   */
  setupBackupRestoreListeners() {
    document
      .getElementById("exportSettingsBtn")
      .addEventListener("click", () => {
        this.exportSettings();
      });

    document
      .getElementById("importSettingsBtn")
      .addEventListener("click", () => {
        this.importSettings();
      });

    document
      .getElementById("resetToDefaultsBtn")
      .addEventListener("click", () => {
        this.confirmAction(
          "Reset all settings to defaults? This cannot be undone.",
          () => {
            this.resetAllSettings();
          }
        );
      });
  }

  /**
   * Set up modal functionality
   */
  setupModal() {
    const modal = document.getElementById("confirmModal");
    const cancelBtn = document.getElementById("modalCancelBtn");
    const confirmBtn = document.getElementById("modalConfirmBtn");

    cancelBtn.addEventListener("click", () => {
      modal.classList.remove("show");
      modal.setAttribute("aria-hidden", "true");
    });

    // Close on escape
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && modal.classList.contains("show")) {
        modal.classList.remove("show");
        modal.setAttribute("aria-hidden", "true");
      }
    });
  }

  /**
   * Initialize UI elements
   */
  initializeUI() {
    // Set initial section
    this.showSection("general");

    // Initialize range values
    const jpegRange = document.getElementById("jpegQuality");
    const jpegValue = document.getElementById("jpegQualityValue");
    jpegValue.textContent = `${jpegRange.value}%`;

    const webpRange = document.getElementById("webpQuality");
    const webpValue = document.getElementById("webpQualityValue");
    webpValue.textContent = `${webpRange.value}%`;
  }
  /**
   * Show specific settings section
   */
  showSection(sectionName) {
    // Prevent rapid section switching
    if (this._switching) return;
    this._switching = true;

    // Update navigation
    document.querySelectorAll(".nav-link").forEach((link) => {
      link.classList.remove("active");
    });
    const navLink = document.querySelector(`[data-section="${sectionName}"]`);
    if (navLink) {
      navLink.classList.add("active");
    }

    // Update content with proper animation handling
    document.querySelectorAll(".settings-section").forEach((section) => {
      section.classList.remove("active");
    });

    // Small delay to ensure previous section is hidden before showing new one
    setTimeout(() => {
      const targetSection = document.querySelector(
        `.settings-section[data-section="${sectionName}"], #${sectionName}`
      );
      if (targetSection) {
        targetSection.classList.add("active");
      }
      // Allow next section switch after animation starts
      setTimeout(() => {
        this._switching = false;
      }, 100);
    }, 10);

    this.currentSection = sectionName;

    // Announce section change
    announceToScreenReader(`Switched to ${sectionName} settings`);
  }

  /**
   * Populate form with current settings
   */
  populateForm() {
    Object.entries(this.settings).forEach(([key, value]) => {
      const element = document.getElementById(key);
      if (!element) return;

      if (element.type === "checkbox") {
        element.checked = value;
      } else if (element.type === "range") {
        element.value = value;
        // Update display
        const valueDisplay = document.getElementById(`${key}Value`);
        if (valueDisplay) {
          valueDisplay.textContent = `${value}%`;
        }
      } else {
        element.value = value;
      }
    });
  }

  /**
   * Save settings from form with debouncing
   */
  async saveSettingsFromForm() {
    const formData = this.getFormData();
    this.settings = { ...this.settings, ...formData };

    try {
      await StorageService.set({ settings: this.settings }, "sync");
      this.logDebug("Settings auto-saved");
    } catch (error) {
      this.handleError("Auto-save error:", error);
    }
  }

  /**
   * Save all settings
   */
  async saveAllSettings() {
    try {
      this.isLoading = true;
      this.setButtonLoading("saveSettingsBtn", true);

      const formData = this.getFormData();
      this.settings = { ...this.settings, ...formData };

      await StorageService.set({ settings: this.settings }, "sync");

      this.showStatus("Settings saved successfully!", "success");
      announceToScreenReader("All settings saved successfully");
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Save error:`, error);
      this.showStatus("Failed to save settings", "error");
    } finally {
      this.isLoading = false;
      this.setButtonLoading("saveSettingsBtn", false);
    }
  }

  /**
   * Get form data as object with proper type conversion
   */
  getFormData() {
    const formData = {};
    const formElements = document.querySelectorAll(
      '.form-control, input[type="checkbox"], input[type="color"]'
    );

    formElements.forEach((element) => {
      if (!element.id) return;

      if (element.type === "checkbox") {
        formData[element.id] = element.checked;
      } else if (element.type === "number" || element.type === "range") {
        formData[element.id] = parseInt(element.value, 10);
      } else {
        formData[element.id] = element.value;
      }
    });

    return formData;
  }

  /**
   * Log debug message if debug is enabled
   */
  logDebug(message) {
    if (DEBUG.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} ${message}`);
    }
  }

  /**
   * Handle errors consistently
   */
  handleError(message, error) {
    console.error(`${EXTENSION_CONFIG.logPrefix} ${message}`, error);
  }

  /**
   * Load and display presets
   */
  async loadPresets() {
    try {
      const presets = await StorageService.getPresets();
      this.displayPresets(presets);
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error loading presets:`,
        error
      );
    }
  }

  /**
   * Display presets in the UI
   */
  displayPresets(presets) {
    const container = document.getElementById("presetsList");
    container.innerHTML = "";

    if (Object.keys(presets).length === 0) {
      container.innerHTML =
        '<p class="setting-description">No presets saved yet.</p>';
      return;
    }

    Object.entries(presets).forEach(([name, preset]) => {
      const presetElement = document.createElement("div");
      presetElement.className = "preset-item";
      presetElement.innerHTML = `
        <div class="preset-info">
          <div class="preset-name">${name}</div>
          <div class="preset-details">
            ${preset.format?.toUpperCase() || "PNG"} • 
            ${preset.quality}x • 
            ${preset.transparentBg ? "Transparent" : "Solid background"}
          </div>
        </div>
        <div class="preset-actions">
          <button class="btn btn-secondary" onclick="settingsController.loadPreset('${name}')">Load</button>
          <button class="btn btn-danger" onclick="settingsController.deletePreset('${name}')">Delete</button>
        </div>
      `;
      container.appendChild(presetElement);
    });
  }

  /**
   * Create new preset from form
   */
  async createPreset() {
    const nameInput = document.getElementById("newPresetName");
    const name = nameInput.value.trim();

    if (!name) {
      this.showStatus("Please enter a preset name", "error");
      return;
    }

    const presetData = {
      quality: document.getElementById("presetQuality").value,
      format: document.getElementById("presetFormat").value,
      bgColor: document.getElementById("presetBackground").value,
      transparentBg: document.getElementById("presetTransparent").checked,
    };

    try {
      await StorageService.savePreset(name, presetData);
      nameInput.value = "";
      this.loadPresets();
      this.showStatus(`Preset "${name}" created!`, "success");
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Preset creation error:`,
        error
      );
      this.showStatus("Failed to create preset", "error");
    }
  }

  /**
   * Load preset into current form
   */
  async loadPreset(name) {
    try {
      const preset = await StorageService.loadPreset(name);
      if (preset) {
        // Apply preset to main form
        Object.entries(preset).forEach(([key, value]) => {
          const element = document.getElementById(key);
          if (element) {
            if (element.type === "checkbox") {
              element.checked = value;
            } else {
              element.value = value;
            }
          }
        });
        this.showStatus(`Preset "${name}" loaded!`, "success");
      }
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Preset load error:`, error);
      this.showStatus("Failed to load preset", "error");
    }
  }

  /**
   * Delete preset
   */
  async deletePreset(name) {
    this.confirmAction(`Delete preset "${name}"?`, async () => {
      try {
        await StorageService.deletePreset(name);
        this.loadPresets();
        this.showStatus(`Preset "${name}" deleted`, "success");
      } catch (error) {
        console.error(
          `${EXTENSION_CONFIG.logPrefix} Preset deletion error:`,
          error
        );
        this.showStatus("Failed to delete preset", "error");
      }
    });
  }

  /**
   * Add dimension preset
   */
  addDimensionPreset() {
    // Implementation for dimension presets
    const width = prompt("Enter width (px):");
    const height = prompt("Enter height (px):");
    const name = prompt("Enter preset name:");

    if (width && height && name) {
      this.dimensionPresets.push({
        name,
        width: parseInt(width),
        height: parseInt(height),
      });
      this.displayDimensionPresets();
    }
  }

  /**
   * Display dimension presets
   */
  displayDimensionPresets() {
    const container = document.getElementById("dimensionPresets");
    container.innerHTML = "";

    this.dimensionPresets.forEach((preset, index) => {
      const element = document.createElement("div");
      element.className = "dimension-preset";
      element.innerHTML = `
        <span>${preset.name} (${preset.width}×${preset.height})</span>
        <button class="btn btn-danger" onclick="settingsController.removeDimensionPreset(${index})">Remove</button>
      `;
      container.appendChild(element);
    });
  }

  /**
   * Remove dimension preset
   */
  removeDimensionPreset(index) {
    this.dimensionPresets.splice(index, 1);
    this.displayDimensionPresets();
  }

  /**
   * Export all settings to file
   */
  async exportSettings() {
    try {
      const allData = {
        settings: this.settings,
        presets: await StorageService.getPresets(),
        dimensionPresets: this.dimensionPresets,
        exportDate: new Date().toISOString(),
        version: EXTENSION_CONFIG.version,
      };

      const blob = new Blob([JSON.stringify(allData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `svg-switcher-settings-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.showStatus("Settings exported successfully!", "success");
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Export error:`, error);
      this.showStatus("Failed to export settings", "error");
    }
  }

  /**
   * Import settings from file
   */
  async importSettings() {
    const fileInput = document.getElementById("importSettingsFile");
    const file = fileInput.files[0];

    if (!file) {
      this.showStatus("Please select a file to import", "error");
      return;
    }

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      // Validate data structure
      if (!data.settings) {
        throw new Error("Invalid settings file format");
      }

      // Import settings
      this.settings = { ...DEFAULT_SETTINGS, ...data.settings };
      await StorageService.set({ settings: this.settings }, "sync");

      // Import presets if available
      if (data.presets) {
        for (const [name, preset] of Object.entries(data.presets)) {
          await StorageService.savePreset(name, preset);
        }
      }

      // Import dimension presets if available
      if (data.dimensionPresets) {
        this.dimensionPresets = data.dimensionPresets;
        this.displayDimensionPresets();
      }

      // Update UI
      this.populateForm();
      this.loadPresets();

      this.showStatus("Settings imported successfully!", "success");
      fileInput.value = "";
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Import error:`, error);
      this.showStatus(
        "Failed to import settings. Please check the file format.",
        "error"
      );
    }
  }

  /**
   * Reset all settings to defaults
   */
  async resetAllSettings() {
    try {
      this.settings = DEFAULT_SETTINGS;
      await StorageService.set({ settings: this.settings }, "sync");

      this.populateForm();
      this.showStatus("All settings reset to defaults", "success");
      announceToScreenReader("All settings have been reset to default values");
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Reset error:`, error);
      this.showStatus("Failed to reset settings", "error");
    }
  }

  /**
   * Show confirmation modal with improved accessibility
   */
  confirmAction(message, callback) {
    const modal = document.getElementById("confirmModal");
    const messageEl = document.getElementById("modalMessage");
    const confirmBtn = document.getElementById("modalConfirmBtn");

    messageEl.textContent = message;

    // Remove old listeners safely
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

    // Add new listener
    newConfirmBtn.addEventListener("click", () => {
      this.hideModal(modal);
      callback();
    });

    this.showModal(modal);
    newConfirmBtn.focus();
  }

  /**
   * Show modal with proper accessibility attributes
   */
  showModal(modal) {
    modal.classList.add("show");
    modal.setAttribute("aria-hidden", "false");

    // Trap focus within modal for accessibility
    this.trapFocusInModal(modal);
  }

  /**
   * Hide modal with proper accessibility cleanup
   */
  hideModal(modal) {
    modal.classList.remove("show");
    modal.setAttribute("aria-hidden", "true");

    // Restore focus to previous element
    this.restoreFocus();
  }

  /**
   * Set up floating action button
   */
  setupFloatingActionButton() {
    const fab = document.getElementById("quickSaveFab");
    if (!fab) return;

    const fabHandler = () => {
      this.saveAllSettings();
      // Add visual feedback
      fab.style.transform = "scale(0.9)";
      setTimeout(() => {
        fab.style.transform = "scale(1)";
      }, 150);
    };

    fab.addEventListener("click", fabHandler);
    return [{ element: fab, event: "click", handler: fabHandler }];
  }

  /**
   * Add loading state to buttons (enhanced version)
   */
  setButtonLoading(buttonId, isLoading) {
    const button = document.getElementById(buttonId);
    if (!button) return;

    if (isLoading) {
      button.disabled = true;
      button.style.position = "relative";

      // Create loading overlay if it doesn't exist
      if (!button.querySelector(".loading-overlay")) {
        const loadingOverlay = document.createElement("div");
        loadingOverlay.className = "loading-overlay";
        loadingOverlay.innerHTML = '<div class="loading-spinner"></div>';
        button.appendChild(loadingOverlay);
      }
    } else {
      button.disabled = false;
      const loadingOverlay = button.querySelector(".loading-overlay");
      if (loadingOverlay) {
        loadingOverlay.remove();
      }
    }
  }

  /**
   * Enhanced status message with better styling
   */
  showStatus(message, type = "info", duration = 3000) {
    const statusElement = document.getElementById("statusMessage");
    if (!statusElement) return;

    // Clear any existing timeout
    if (this.statusTimeout) {
      clearTimeout(this.statusTimeout);
    }

    // Set the message and type
    statusElement.textContent = message;
    statusElement.className = `status-message ${type} show`;

    // Auto-hide after duration
    this.statusTimeout = setTimeout(() => {
      statusElement.classList.remove("show");
    }, duration);
  }

  /**
   * Cleanup resources on page unload
   */
  cleanup() {
    // Remove all registered event listeners
    Object.values(this.eventHandlers)
      .flat()
      .forEach((handler) => {
        if (handler && handler.element && handler.event) {
          handler.element.removeEventListener(handler.event, handler.handler);
        }
      });

    // Clear timeouts
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }

    if (this.statusTimeout) {
      clearTimeout(this.statusTimeout);
    }

    this.logDebug("Settings controller cleaned up");
  }
}

// Initialize settings controller
const settingsController = new SettingsController();
settingsController.init().catch((error) => {
  console.error("Failed to initialize settings:", error);
});

// Initialize header enhancer with performance checks
document.addEventListener("DOMContentLoaded", () => {
  // Only initialize if not on mobile for performance
  if (
    window.innerWidth > 768 &&
    !window.matchMedia("(prefers-reduced-motion: reduce)").matches
  ) {
    new HeaderEnhancer();
  }
});

// Cleanup on page unload
window.addEventListener("beforeunload", () => {
  settingsController.cleanup();
});

// Make controller available globally for onclick handlers
window.settingsController = settingsController;

// Enhanced Header Interactivity (Optimized)
class HeaderEnhancer {
  constructor() {
    this.header = document.getElementById("dynamicHeader");
    this.cache = new Map();
    this.animationFrame = null;
    this.mouseCoords = { x: 0, y: 0 };
    this.particles = [];

    if (this.header) this.init();
  }

  // Cached DOM queries for performance
  getElement(selector, useCache = true) {
    if (useCache && this.cache.has(selector)) {
      return this.cache.get(selector);
    }
    const element = this.header.querySelector(selector);
    if (useCache) this.cache.set(selector, element);
    return element;
  }

  getElements(selector, useCache = true) {
    const cacheKey = `${selector}:all`;
    if (useCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    const elements = this.header.querySelectorAll(selector);
    if (useCache) this.cache.set(cacheKey, elements);
    return elements;
  }

  init() {
    this.generateDynamicElements();
    this.setupEventListeners();
    this.setupParticleSystem();
    this.startAnimationLoop();
    this.enhanceThemeTransitions();
  }

  // Generate dynamic elements to reduce HTML redundancy
  generateDynamicElements() {
    this.generatePatternElements();
    this.generateParticleElements();
    this.generateStarElements();
    this.generateSparkleElements();
  }

  generatePatternElements() {
    const patternGrid = this.getElement(".pattern-grid");
    if (!patternGrid) return;

    const patterns = [
      { type: "line", class: "pattern-line", count: 3 },
      { type: "circle", class: "pattern-circle", count: 2 },
      { type: "triangle", class: "pattern-triangle", count: 2 },
    ];

    patterns.forEach(({ type, class: className, count }) => {
      for (let i = 0; i < count; i++) {
        const element = document.createElement("div");
        element.className = `${className} pattern-element`;
        patternGrid.appendChild(element);
      }
    });
  }

  generateParticleElements() {
    const particleLayers = this.getElements(".particle-layer");

    particleLayers.forEach((layer) => {
      const count = parseInt(layer.dataset.particleCount) || 5;
      const type = layer.dataset.particleType || "interactive";

      for (let i = 0; i < count; i++) {
        const particle = document.createElement("div");
        particle.className = `particle ${type}-particle particle-base`;

        if (type === "interactive") {
          particle.dataset.speed = (0.7 + Math.random() * 0.6).toFixed(1);
        }

        layer.appendChild(particle);
      }
    });
  }

  generateStarElements() {
    const starfield = this.getElement(".starfield");
    if (!starfield) return;

    const count = parseInt(starfield.dataset.starCount) || 10;

    for (let i = 0; i < count; i++) {
      const star = document.createElement("div");
      star.className = "star particle-base";
      starfield.appendChild(star);
    }
  }

  generateSparkleElements() {
    const sparkleContainer = this.getElement(".icon-sparkles");
    if (!sparkleContainer) return;

    const count = parseInt(sparkleContainer.dataset.sparkleCount) || 4;

    for (let i = 0; i < count; i++) {
      const sparkle = document.createElement("div");
      sparkle.className = `sparkle sparkle-${i + 1}`;
      sparkleContainer.appendChild(sparkle);
    }
  }

  setupEventListeners() {
    // Consolidated mouse tracking with throttling
    let mouseTimeout;
    this.header.addEventListener("mousemove", (e) => {
      if (mouseTimeout) return;
      mouseTimeout = setTimeout(() => (mouseTimeout = null), 16); // ~60fps throttling

      const rect = this.header.getBoundingClientRect();
      this.mouseCoords.x = (e.clientX - rect.left) / rect.width;
      this.mouseCoords.y = (e.clientY - rect.top) / rect.height;
    });

    // Enhanced interactive elements
    this.setupInteractiveElements();
  }

  setupInteractiveElements() {
    // Consolidated button and title interactions
    const interactiveSelectors = [
      { selector: ".enhanced-btn", events: ["mouseenter", "click"] },
      { selector: ".title-word", events: ["mouseenter", "mouseleave"] },
      { selector: ".title-icon", events: ["mouseenter"] },
    ];

    interactiveSelectors.forEach(({ selector, events }) => {
      this.getElements(selector).forEach((element) => {
        events.forEach((event) => {
          element.addEventListener(event, (e) =>
            this.handleInteraction(element, event, e)
          );
        });
      });
    });
  }

  handleInteraction(element, event, e) {
    const handlers = {
      "enhanced-btn": {
        mouseenter: () =>
          this.animateParticles(element.querySelectorAll(".btn-particle")),
        click: () => this.createRippleEffect(element, e),
      },
      "title-word": {
        mouseenter: () =>
          this.applyTransform(element, "translateY(-10px) scale(1.05)"),
        mouseleave: () =>
          this.applyTransform(element, "translateY(0) scale(1)"),
      },
      "title-icon": {
        mouseenter: () => this.animateParticles(this.getElements(".sparkle")),
      },
    };

    const className = Array.from(element.classList).find(
      (cls) => handlers[cls]
    );
    if (className && handlers[className][event]) {
      handlers[className][event]();
    }
  }

  setupParticleSystem() {
    this.getElements(".interactive-particle").forEach((particle, index) => {
      this.particles.push({
        element: particle,
        speed: parseFloat(particle.dataset.speed) || 1,
        angle: Math.random() * Math.PI * 2,
        baseOffset: { x: Math.random() * 10, y: Math.random() * 10 },
      });
    });
  }

  updateParallaxEffects() {
    // Update geometric patterns based on mouse position
    const patternGrid = this.header.querySelector(".pattern-grid");
    if (patternGrid) {
      const moveX = (this.mouseX - 0.5) * 20;
      const moveY = (this.mouseY - 0.5) * 20;
      patternGrid.style.transform = `translate(${moveX}px, ${moveY}px)`;
    }

    // Update gradient mesh blobs
    const blobs = this.header.querySelectorAll(".mesh-blob");
    blobs.forEach((blob, index) => {
      const intensity = (index + 1) * 0.5;
      const moveX = (this.mouseX - 0.5) * intensity * 30;
      const moveY = (this.mouseY - 0.5) * intensity * 30;
      blob.style.transform = `translate(${moveX}px, ${moveY}px)`;
    });

    // Update icon container
    const iconContainer = this.header.querySelector(".title-icon-container");
    if (iconContainer) {
      const tiltX = (this.mouseY - 0.5) * 10;
      const tiltY = (this.mouseX - 0.5) * -10;
      iconContainer.style.transform = `perspective(1000px) rotateX(${tiltX}deg) rotateY(${tiltY}deg)`;
    }
  }

  updateLightRays() {
    const lightRays = this.header.querySelectorAll(".light-ray");
    lightRays.forEach((ray, index) => {
      const intensity = Math.sin(Date.now() * 0.001 + index) * 0.5 + 0.5;
      const mouseInfluence = Math.sqrt(
        Math.pow(this.mouseX - 0.5, 2) + Math.pow(this.mouseY - 0.5, 2)
      );
      ray.style.opacity = (intensity * 0.5 + mouseInfluence * 0.3).toString();
    });
  }

  setupDynamicEffects() {
    // Dynamic color shifting for gradient
    const gradientBar = this.header.querySelector(".header-gradient");
    if (gradientBar) {
      setInterval(() => {
        const hue = (Date.now() * 0.05) % 360;
        gradientBar.style.setProperty("--dynamic-hue", hue + "deg");
      }, 50);
    }

    // Enhanced button effects
    const enhancedBtns = this.header.querySelectorAll(".enhanced-btn");
    enhancedBtns.forEach((btn) => {
      btn.addEventListener("mouseenter", () => {
        this.createButtonParticles(btn);
      });

      btn.addEventListener("click", (e) => {
        this.createRippleEffect(btn, e);
      });
    });
  }

  createButtonParticles(button) {
    const particles = button.querySelectorAll(".btn-particle");
    particles.forEach((particle, index) => {
      particle.style.animation = "none";
      particle.offsetHeight; // Trigger reflow
      particle.style.animation = `buttonParticle 1s ease-out ${index * 0.1}s`;
    });
  }

  createRippleEffect(button, event) {
    const ripple = button.querySelector(".btn-ripple");
    if (!ripple) return;

    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + "px";
    ripple.style.left = x + "px";
    ripple.style.top = y + "px";
    ripple.style.transform = "scale(0)";
    ripple.style.opacity = "0.6";

    requestAnimationFrame(() => {
      ripple.style.transform = "scale(1)";
      ripple.style.opacity = "0";
    });
  }

  setupTitleAnimations() {
    const titleWords = this.header.querySelectorAll(".title-word");
    titleWords.forEach((word, index) => {
      word.addEventListener("mouseenter", () => {
        word.style.transform = "translateY(-10px) scale(1.05)";
        word.style.transition =
          "all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55)";
      });

      word.addEventListener("mouseleave", () => {
        word.style.transform = "translateY(0) scale(1)";
      });
    });

    // Sparkle effects on icon hover
    const titleIcon = this.header.querySelector(".title-icon");
    if (titleIcon) {
      titleIcon.addEventListener("mouseenter", () => {
        this.activateSparkles();
      });
    }
  }

  activateSparkles() {
    const sparkles = this.header.querySelectorAll(".sparkle");
    sparkles.forEach((sparkle, index) => {
      sparkle.style.animation = "none";
      sparkle.offsetHeight; // Trigger reflow
      sparkle.style.animation = `sparkleAnimation 0.6s ease-out ${
        index * 0.1
      }s`;
    });
  }

  // Unified animation function for particles and sparkles
  animateParticles(particles) {
    particles.forEach((particle, index) => {
      particle.style.animation = "none";
      particle.offsetHeight; // Force reflow
      particle.style.animation = `universalFloat 1s ease-out ${index * 0.1}s`;
    });
  }

  applyTransform(element, transform) {
    element.style.transform = transform;
    element.style.transition = "all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55)";
  }

  // Consolidated animation loop
  startAnimationLoop() {
    const updateFunctions = [
      () => this.updateParallaxEffects(),
      () => this.updateParticleSystem(),
      () => this.updateDynamicEffects(),
    ];

    const animate = (timestamp) => {
      updateFunctions.forEach((fn) => fn());
      this.animationFrame = requestAnimationFrame(animate);
    };

    this.animationFrame = requestAnimationFrame(animate);
  }

  updateParallaxEffects() {
    const { x, y } = this.mouseCoords;
    const centerOffset = { x: x - 0.5, y: y - 0.5 };

    // Update multiple elements with mouse influence
    const parallaxElements = [
      { element: this.getElement(".pattern-grid"), intensity: 20 },
      {
        element: this.getElement(".title-icon-container"),
        intensity: 10,
        use3D: true,
      },
    ];

    parallaxElements.forEach(({ element, intensity, use3D }) => {
      if (!element) return;

      const moveX = centerOffset.x * intensity;
      const moveY = centerOffset.y * intensity;

      if (use3D) {
        element.style.transform = `perspective(1000px) rotateX(${moveY}deg) rotateY(${-moveX}deg)`;
      } else {
        element.style.transform = `translate(${moveX}px, ${moveY}px)`;
      }
    });

    // Update mesh blobs
    this.getElements(".mesh-blob").forEach((blob, index) => {
      const intensity = (index + 1) * 15;
      const moveX = centerOffset.x * intensity;
      const moveY = centerOffset.y * intensity;
      blob.style.transform = `translate(${moveX}px, ${moveY}px)`;
    });
  }

  updateParticleSystem() {
    this.particles.forEach((particle) => {
      particle.angle += 0.01 * particle.speed;

      const influence = this.calculateMouseInfluence(particle);
      const offsetX =
        Math.cos(particle.angle) * 10 + influence.x + particle.baseOffset.x;
      const offsetY =
        Math.sin(particle.angle) * 10 + influence.y + particle.baseOffset.y;

      particle.element.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${
        1 + influence.scale
      })`;
    });
  }

  calculateMouseInfluence(particle) {
    const rect = this.header.getBoundingClientRect();
    const particleRect = particle.element.getBoundingClientRect();

    const particleCoords = {
      x: (particleRect.left - rect.left) / rect.width,
      y: (particleRect.top - rect.top) / rect.height,
    };

    const distance = Math.hypot(
      this.mouseCoords.x - particleCoords.x,
      this.mouseCoords.y - particleCoords.y
    );

    const influence = Math.max(0, 1 - distance * 2);

    return {
      x: (this.mouseCoords.x - particleCoords.x) * influence * 20,
      y: (this.mouseCoords.y - particleCoords.y) * influence * 20,
      scale: influence * 0.3,
    };
  }

  updateDynamicEffects() {
    const time = Date.now() * 0.001;

    // Update light rays with mouse influence
    const mouseInfluence = Math.hypot(
      this.mouseCoords.x - 0.5,
      this.mouseCoords.y - 0.5
    );
    this.getElements(".light-ray").forEach((ray, index) => {
      const intensity = Math.sin(time + index) * 0.5 + 0.5;
      ray.style.opacity = (intensity * 0.5 + mouseInfluence * 0.3).toString();
    });

    // Update stars with subtle animation
    this.getElements(".star").forEach((star, index) => {
      const twinkle = Math.sin(time * 2 + index) * 0.5 + 0.5;
      star.style.opacity = (0.3 + twinkle * 0.7).toString();
    });

    // Update wave effects
    this.getElements(".morphing-path").forEach((path, index) => {
      const wave = Math.sin(time + index * 2) * 0.1 + 1;
      path.style.transform = `scaleY(${wave})`;
    });
  }

  enhanceThemeTransitions() {
    const style = document.createElement("style");
    style.textContent = `
      .settings-header * {
        transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
      }
      .settings-header.theme-transition {
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      }
    `;
    document.head.appendChild(style);
  }

  // Cleanup method for performance
  destroy() {
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }
    this.cache.clear();
  }
}
