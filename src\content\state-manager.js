/**
 * State Manager for Content Script
 * Handles SVG selection state and caching
 */

import { CSS_CLASSES } from "../config/constants.js";

/**
 * Manages the state of selected SVGs and related data
 */
export class StateManager {
  constructor() {
    this.selectedSVGs = new Set();
    this.isInitialized = false;
    this.toolbar = null;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.cachedData = null;
  }

  /**
   * Add SVG element to selection
   * @param {Element} svgElement - SVG element to add
   */
  addSelection(svgElement) {
    this.selectedSVGs.add(svgElement);
    this.clearCache();
  }

  /**
   * Remove SVG element from selection
   * @param {Element} svgElement - SVG element to remove
   */
  removeSelection(svgElement) {
    this.selectedSVGs.delete(svgElement);
    this.clearCache();
  }

  /**
   * Clear all selections and update UI
   */
  clearAllSelections() {
    this.selectedSVGs.forEach((svg) => {
      svg.classList.remove(CSS_CLASSES.selected);
    });
    this.selectedSVGs.clear();
    this.clearCache();
  }

  /**
   * Get current selection count
   * @returns {number} Number of selected SVG elements
   */
  getSelectionCount() {
    return this.selectedSVGs.size;
  }

  /**
   * Check if SVG element is selected
   * @param {Element} svgElement - SVG element to check
   * @returns {boolean} Whether element is selected
   */
  isSelected(svgElement) {
    return this.selectedSVGs.has(svgElement);
  }

  /**
   * Get all selected SVG elements
   * @returns {Set<Element>} Set of selected SVG elements
   */
  getSelectedSVGs() {
    return new Set(this.selectedSVGs);
  }

  /**
   * Clear cached data
   */
  clearCache() {
    this.cachedData = null;
  }

  /**
   * Set cached data
   * @param {*} data - Data to cache
   */
  setCachedData(data) {
    this.cachedData = data;
  }

  /**
   * Get cached data
   * @returns {*} Cached data or null
   */
  getCachedData() {
    return this.cachedData;
  }

  /**
   * Set toolbar reference
   * @param {Element} toolbar - Toolbar element
   */
  setToolbar(toolbar) {
    this.toolbar = toolbar;
  }

  /**
   * Get toolbar reference
   * @returns {Element|null} Toolbar element
   */
  getToolbar() {
    return this.toolbar;
  }

  /**
   * Set drag state
   * @param {boolean} isDragging - Whether currently dragging
   * @param {Object} offset - Drag offset coordinates
   */
  setDragState(isDragging, offset = null) {
    this.isDragging = isDragging;
    if (offset) {
      this.dragOffset = offset;
    }
  }

  /**
   * Get drag state
   * @returns {Object} Drag state information
   */
  getDragState() {
    return {
      isDragging: this.isDragging,
      offset: this.dragOffset,
    };
  }

  /**
   * Mark as initialized
   */
  setInitialized() {
    this.isInitialized = true;
  }

  /**
   * Check if initialized
   * @returns {boolean} Whether state manager is initialized
   */
  getInitialized() {
    return this.isInitialized;
  }

  /**
   * Reset all state
   */
  reset() {
    this.clearAllSelections();
    this.toolbar = null;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.isInitialized = false;
  }
}
