/**
 * Performance Utilities
 * Tools for monitoring and measuring extension performance
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";

/**
 * Performance monitoring and benchmarking utilities
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.timers = new Map();
    this.memoryBaseline = null;
    this.isEnabled = DEBUG.enabled;
  }

  /**
   * Start timing an operation
   * @param {string} label - Operation label
   */
  startTimer(label) {
    if (!this.isEnabled) return;

    this.timers.set(label, {
      start: performance.now(),
      memory: this._getMemoryUsage(),
    });
  }

  /**
   * End timing an operation and record metrics
   * @param {string} label - Operation label
   * @returns {Object} Performance metrics
   */
  endTimer(label) {
    if (!this.isEnabled) return null;

    const timer = this.timers.get(label);
    if (!timer) {
      console.warn(`${EXTENSION_CONFIG.logPrefix} Timer '${label}' not found`);
      return null;
    }

    const endTime = performance.now();
    const endMemory = this._getMemoryUsage();

    const metrics = {
      label,
      duration: endTime - timer.start,
      memoryStart: timer.memory,
      memoryEnd: endMemory,
      memoryDelta: endMemory - timer.memory,
      timestamp: new Date().toISOString(),
    };

    this._recordMetrics(label, metrics);
    this.timers.delete(label);

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Performance [${label}]:`,
        `${metrics.duration.toFixed(2)}ms`,
        `Memory: ${this._formatBytes(metrics.memoryDelta)}`
      );
    }

    return metrics;
  }

  /**
   * Measure function execution time
   * @param {string} label - Operation label
   * @param {Function} fn - Function to measure
   * @returns {*} Function result
   */
  async measure(label, fn) {
    if (!this.isEnabled) {
      return await fn();
    }

    this.startTimer(label);
    try {
      const result = await fn();
      this.endTimer(label);
      return result;
    } catch (error) {
      this.endTimer(label);
      throw error;
    }
  }

  /**
   * Record custom metric
   * @param {string} label - Metric label
   * @param {*} value - Metric value
   * @param {string} unit - Unit of measurement
   */
  recordMetric(label, value, unit = "") {
    if (!this.isEnabled) return;

    const metric = {
      label,
      value,
      unit,
      timestamp: new Date().toISOString(),
      memory: this._getMemoryUsage(),
    };

    this._recordMetrics(label, metric);

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Metric [${label}]:`,
        `${value}${unit}`
      );
    }
  }

  /**
   * Get performance summary
   * @returns {Object} Performance summary
   */
  getSummary() {
    const summary = {
      totalMetrics: this.metrics.size,
      memoryUsage: this._getMemoryUsage(),
      categories: {},
    };

    this.metrics.forEach((metricList, label) => {
      const category = label.split("_")[0] || "general";

      if (!summary.categories[category]) {
        summary.categories[category] = {
          count: 0,
          avgDuration: 0,
          totalDuration: 0,
          avgMemoryDelta: 0,
          metrics: [],
        };
      }

      const cat = summary.categories[category];
      cat.count += metricList.length;

      metricList.forEach((metric) => {
        if (metric.duration !== undefined) {
          cat.totalDuration += metric.duration;
          cat.avgDuration = cat.totalDuration / cat.count;
        }
        if (metric.memoryDelta !== undefined) {
          cat.avgMemoryDelta += metric.memoryDelta;
        }
        cat.metrics.push(metric);
      });
    });

    return summary;
  }

  /**
   * Export metrics as JSON
   * @returns {string} JSON string of all metrics
   */
  exportMetrics() {
    const data = {
      summary: this.getSummary(),
      rawMetrics: Object.fromEntries(this.metrics),
      exportTime: new Date().toISOString(),
      userAgent: navigator.userAgent,
    };

    return JSON.stringify(data, null, 2);
  }

  /**
   * Clear all metrics
   */
  clearMetrics() {
    this.metrics.clear();
    this.timers.clear();

    if (DEBUG.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} Performance metrics cleared`);
    }
  }

  /**
   * Set memory baseline for comparison
   */
  setMemoryBaseline() {
    this.memoryBaseline = this._getMemoryUsage();

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Memory baseline set:`,
        this._formatBytes(this.memoryBaseline)
      );
    }
  }

  /**
   * Get memory usage compared to baseline
   * @returns {Object} Memory comparison
   */
  getMemoryComparison() {
    const current = this._getMemoryUsage();
    const baseline = this.memoryBaseline || current;

    return {
      current,
      baseline,
      delta: current - baseline,
      percentage: baseline > 0 ? ((current - baseline) / baseline) * 100 : 0,
    };
  }

  /**
   * Record metrics internally
   * @param {string} label - Metric label
   * @param {Object} metric - Metric data
   * @private
   */
  _recordMetrics(label, metric) {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }

    const metricList = this.metrics.get(label);
    metricList.push(metric);

    // Keep only last 100 metrics per label
    if (metricList.length > 100) {
      metricList.shift();
    }
  }

  /**
   * Get current memory usage
   * @returns {number} Memory usage in bytes
   * @private
   */
  _getMemoryUsage() {
    try {
      if (performance.memory) {
        return performance.memory.usedJSHeapSize;
      }
      return 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Format bytes to human readable string
   * @param {number} bytes - Bytes to format
   * @returns {string} Formatted string
   * @private
   */
  _formatBytes(bytes) {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));

    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${sizes[i]}`;
  }
}

// Create singleton instance
let performanceMonitorInstance = null;

/**
 * Get performance monitor singleton
 * @returns {PerformanceMonitor} Performance monitor instance
 */
export function getPerformanceMonitor() {
  if (!performanceMonitorInstance) {
    performanceMonitorInstance = new PerformanceMonitor();
  }
  return performanceMonitorInstance;
}

/**
 * Convenience function to measure async operations
 * @param {string} label - Operation label
 * @param {Function} fn - Async function to measure
 * @returns {*} Function result
 */
export async function measureAsync(label, fn) {
  const monitor = getPerformanceMonitor();
  return await monitor.measure(label, fn);
}

/**
 * Convenience function to measure sync operations
 * @param {string} label - Operation label
 * @param {Function} fn - Function to measure
 * @returns {*} Function result
 */
export function measureSync(label, fn) {
  const monitor = getPerformanceMonitor();
  monitor.startTimer(label);
  try {
    const result = fn();
    monitor.endTimer(label);
    return result;
  } catch (error) {
    monitor.endTimer(label);
    throw error;
  }
}

/**
 * Benchmark comparison utility
 */
export class BenchmarkComparison {
  constructor() {
    this.results = new Map();
  }

  /**
   * Add benchmark result
   * @param {string} name - Benchmark name
   * @param {Object} result - Benchmark result
   */
  addResult(name, result) {
    this.results.set(name, result);
  }

  /**
   * Compare two benchmark results
   * @param {string} baseline - Baseline benchmark name
   * @param {string} comparison - Comparison benchmark name
   * @returns {Object} Comparison results
   */
  compare(baseline, comparison) {
    const baseResult = this.results.get(baseline);
    const compResult = this.results.get(comparison);

    if (!baseResult || !compResult) {
      throw new Error("Both benchmark results must exist for comparison");
    }

    return {
      baseline: baseResult,
      comparison: compResult,
      improvement: {
        duration:
          ((baseResult.duration - compResult.duration) / baseResult.duration) *
          100,
        memory:
          baseResult.memoryDelta && compResult.memoryDelta
            ? ((baseResult.memoryDelta - compResult.memoryDelta) /
                baseResult.memoryDelta) *
              100
            : 0,
      },
      summary: this._generateComparisonSummary(baseResult, compResult),
    };
  }

  /**
   * Generate comparison summary
   * @param {Object} baseline - Baseline result
   * @param {Object} comparison - Comparison result
   * @returns {string} Summary text
   * @private
   */
  _generateComparisonSummary(baseline, comparison) {
    const durationImprovement =
      ((baseline.duration - comparison.duration) / baseline.duration) * 100;
    const memoryImprovement =
      baseline.memoryDelta && comparison.memoryDelta
        ? ((baseline.memoryDelta - comparison.memoryDelta) /
            baseline.memoryDelta) *
          100
        : 0;

    let summary = `Performance comparison:\n`;
    summary += `Duration: ${
      durationImprovement > 0 ? "improved" : "degraded"
    } by ${Math.abs(durationImprovement).toFixed(1)}%\n`;

    if (memoryImprovement !== 0) {
      summary += `Memory: ${
        memoryImprovement > 0 ? "improved" : "degraded"
      } by ${Math.abs(memoryImprovement).toFixed(1)}%`;
    }

    return summary;
  }
}
