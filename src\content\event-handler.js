/**
 * Event Handler for Content Script
 * Manages all DOM events and user interactions
 */

import { CSS_CLASSES, EXTENSION_CONFIG } from "../config/constants.js";
import { StorageService } from "../services/storage.service.js";

/**
 * Handles all DOM events for SVG selection
 */
export class EventHandler {
  constructor(stateManager, toolbarManager, svgUtils) {
    this.stateManager = stateManager;
    this.toolbarManager = toolbarManager;
    this.svgUtils = svgUtils;
    this.eventListeners = new Map();
    this.isAttached = false;
    this.settings = {
      selectionModifier: "ctrl",
      clearOnEscape: true,
      visualFeedback: true,
    };

    // Load settings on initialization
    this.loadSettings();

    // Listen for settings changes
    this.setupStorageListener();
  }

  /**
   * Load settings from storage
   */
  async loadSettings() {
    try {
      const stored = await StorageService.get(["settings"], "sync");
      if (stored.settings) {
        this.settings = {
          selectionModifier: stored.settings.selectionModifier || "ctrl",
          clearOnEscape: stored.settings.clearOnEscape !== false,
          visualFeedback: stored.settings.visualFeedback !== false,
        };
      }

      if (EXTENSION_CONFIG.debug?.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Settings loaded:`,
          this.settings
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to load settings:`,
        error
      );
      // Use defaults if loading fails
    }
  }

  /**
   * Set up storage listener to reload settings when they change
   */
  setupStorageListener() {
    if (chrome.storage && chrome.storage.onChanged) {
      this.storageListener = (changes, areaName) => {
        if (areaName === "sync" && changes.settings) {
          this.loadSettings();
          if (EXTENSION_CONFIG.debug?.enabled) {
            console.log(
              `${EXTENSION_CONFIG.logPrefix} Settings updated from storage`
            );
          }
        }
      };
      chrome.storage.onChanged.addListener(this.storageListener);
    }
  }

  /**
   * Check if the correct modifier key is pressed based on settings
   * @param {MouseEvent} event - Click event
   * @returns {boolean} Whether the correct modifier is pressed
   * @private
   */
  _isCorrectModifierPressed(event) {
    switch (this.settings.selectionModifier) {
      case "ctrl":
        return event.ctrlKey || event.metaKey; // Include metaKey for Mac
      case "shift":
        return event.shiftKey;
      case "alt":
        return event.altKey;
      case "none":
        return true; // No modifier required
      default:
        return event.ctrlKey || event.metaKey; // Default to ctrl
    }
  }

  /**
   * Attach all event listeners
   */
  attachEventListeners() {
    if (this.isAttached) return;

    this._addEventListeners();
    this.isAttached = true;
  }

  /**
   * Remove all event listeners
   */
  removeEventListeners() {
    if (!this.isAttached) return;

    this._removeEventListeners();
    this.isAttached = false;
  }

  /**
   * Add all event listeners with proper cleanup tracking
   * @private
   */
  _addEventListeners() {
    // Click event for SVG selection
    const clickHandler = (e) => this._handleSVGClick(e);
    document.addEventListener("click", clickHandler, true);
    this.eventListeners.set("click", clickHandler);

    // Keyboard events
    const keydownHandler = (e) => this._handleKeyDown(e);
    document.addEventListener("keydown", keydownHandler);
    this.eventListeners.set("keydown", keydownHandler);

    // Prevent context menu on selected SVGs
    const contextMenuHandler = (e) => this._handleContextMenu(e);
    document.addEventListener("contextmenu", contextMenuHandler);
    this.eventListeners.set("contextmenu", contextMenuHandler);

    // Handle page navigation/unload
    const unloadHandler = () => this._handlePageUnload();
    window.addEventListener("beforeunload", unloadHandler);
    this.eventListeners.set("beforeunload", unloadHandler);

    // Handle visibility changes
    const visibilityHandler = () => this._handleVisibilityChange();
    document.addEventListener("visibilitychange", visibilityHandler);
    this.eventListeners.set("visibilitychange", visibilityHandler);
  }

  /**
   * Remove all tracked event listeners
   * @private
   */
  _removeEventListeners() {
    this.eventListeners.forEach((handler, event) => {
      if (event === "beforeunload") {
        window.removeEventListener(event, handler);
      } else if (event === "click") {
        document.removeEventListener(event, handler, true);
      } else {
        document.removeEventListener(event, handler);
      }
    });
    this.eventListeners.clear();
  }

  /**
   * Handle SVG click events
   * @param {MouseEvent} event - Click event
   * @private
   */
  _handleSVGClick(event) {
    // Only handle clicks with Ctrl/Cmd modifier
    if (!this._isCorrectModifierPressed(event)) return;

    const svgElement = this._findSVGElement(event.target);
    if (!svgElement) return;

    event.preventDefault();
    event.stopPropagation();

    if (EXTENSION_CONFIG.debug?.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} SVG clicked:`, {
        target: event.target.tagName,
        svgElement: svgElement,
        ctrlKey: event.ctrlKey,
        metaKey: event.metaKey,
      });
    }

    this._toggleSVGSelection(svgElement);
  }

  /**
   * Handle keyboard events
   * @param {KeyboardEvent} event - Keyboard event
   * @private
   */
  _handleKeyDown(event) {
    switch (event.key) {
      case "Escape":
        if (
          this.settings.clearOnEscape &&
          this.stateManager.getSelectionCount() > 0
        ) {
          event.preventDefault();
          this.stateManager.clearAllSelections();
          this.toolbarManager.updateToolbar().catch(console.error);
        }
        break;

      case "d":
      case "D":
        // Ctrl+D to download selected SVGs
        if (
          (event.ctrlKey || event.metaKey) &&
          this.stateManager.getSelectionCount() > 0
        ) {
          event.preventDefault();
          this._triggerDownload();
        }
        break;
    }
  }

  /**
   * Handle context menu events
   * @param {MouseEvent} event - Context menu event
   * @private
   */
  _handleContextMenu(event) {
    const svgElement = this._findSVGElement(event.target);
    if (svgElement && this.stateManager.isSelected(svgElement)) {
      // Prevent context menu on selected SVGs to avoid accidental deselection
      event.preventDefault();
    }
  }

  /**
   * Handle page unload
   * @private
   */
  _handlePageUnload() {
    this.cleanup();
  }

  /**
   * Handle visibility change
   * @private
   */
  _handleVisibilityChange() {
    if (document.hidden) {
      // Page is hidden, clean up resources
      this.toolbarManager.hideToolbar();
    } else {
      // Page is visible again, restore toolbar if needed
      if (this.stateManager.getSelectionCount() > 0) {
        this.toolbarManager.updateToolbar();
      }
    }
  }

  /**
   * Find SVG element from event target
   * @param {Element} target - Event target element
   * @returns {Element|null} SVG element or null
   * @private
   */
  _findSVGElement(target) {
    if (!target || !target.tagName) return null;

    // Check if target is SVG or has SVG parent
    let element = target;
    while (
      element &&
      element !== document.body &&
      element !== document.documentElement
    ) {
      if (element.tagName && element.tagName.toLowerCase() === "svg") {
        return element;
      }
      // Also check for SVG children (elements inside SVG)
      if (element.closest && element.closest("svg")) {
        return element.closest("svg");
      }
      element = element.parentElement;
    }
    return null;
  }

  /**
   * Toggle SVG selection state
   * @param {Element} svgElement - SVG element to toggle
   * @private
   */
  _toggleSVGSelection(svgElement) {
    const isSelected = this.stateManager.isSelected(svgElement);

    if (isSelected) {
      // Deselect
      if (this.settings.visualFeedback) {
        svgElement.classList.remove(CSS_CLASSES.selected);
      }
      this.stateManager.removeSelection(svgElement);
    } else {
      // Select
      if (this.settings.visualFeedback) {
        svgElement.classList.add(CSS_CLASSES.selected);
      }
      this.stateManager.addSelection(svgElement);
    }

    // Update toolbar
    this.toolbarManager.updateToolbar().catch(console.error);

    // Create toolbar if first selection
    if (!isSelected && this.stateManager.getSelectionCount() === 1) {
      this.toolbarManager.createToolbar().catch(console.error);
    }
  }

  /**
   * Trigger download action
   * @private
   */
  _triggerDownload() {
    chrome.runtime
      .sendMessage({
        action: "downloadFromToolbar",
      })
      .catch((error) => {
        console.error(
          `${EXTENSION_CONFIG.logPrefix} Error triggering download:`,
          error
        );
      });
  }

  /**
   * Clean up all event listeners and resources
   */
  cleanup() {
    this.removeEventListeners();
    this.eventListeners.clear();
    this.isAttached = false;

    // Clean up storage listener
    if (chrome.storage && chrome.storage.onChanged && this.storageListener) {
      chrome.storage.onChanged.removeListener(this.storageListener);
    }
  }

  /**
   * Check if event listeners are attached
   * @returns {boolean} Whether event listeners are attached
   */
  isListenersAttached() {
    return this.isAttached;
  }
}
