/**
 * Storage Service
 * Handles all Chrome storage operations for the extension
 */

import {
  STORAGE_KEYS,
  ERROR_MESSAGES,
  EXTENSION_CONFIG,
  DEBUG,
} from "../config/constants.js";

/**
 * Storage service class for managing extension data
 */
export class StorageService {
  /**
   * Get data from Chrome storage
   * @param {string|string[]|Object} keys - Storage keys to retrieve
   * @param {string} area - Storage area ('sync' or 'local')
   * @returns {Promise<Object>} Retrieved data
   */
  static async get(keys, area = "sync") {
    try {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Getting storage data:`,
          keys
        );
      }

      const result = await chrome.storage[area].get(keys);

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Retrieved storage data:`,
          result
        );
      }

      return result;
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Storage get error:`, error);
      throw new Error(`${ERROR_MESSAGES.storageError}: ${error.message}`);
    }
  }

  /**
   * Set data in Chrome storage
   * @param {Object} data - Data to store
   * @param {string} area - Storage area ('sync' or 'local')
   * @returns {Promise<void>}
   */
  static async set(data, area = "sync") {
    try {
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Setting storage data:`,
          data
        );
      }

      await chrome.storage[area].set(data);

      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Storage data set successfully`
        );
      }
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Storage set error:`, error);
      throw new Error(`${ERROR_MESSAGES.storageError}: ${error.message}`);
    }
  }

  /**
   * Get all saved presets
   * @returns {Promise<Object>} Object containing all presets
   */
  static async getPresets() {
    try {
      const result = await this.get(STORAGE_KEYS.presets);
      return result[STORAGE_KEYS.presets] || {};
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error getting presets:`,
        error
      );
      return {};
    }
  }

  /**
   * Save a new preset or update existing one
   * @param {string} name - Preset name
   * @param {Object} settings - Preset settings
   * @returns {Promise<boolean>} Success status
   */
  static async savePreset(name, settings) {
    try {
      if (!name || !settings) {
        throw new Error("Preset name and settings are required");
      }

      const presets = await this.getPresets();
      presets[name] = {
        ...settings,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      await this.set({ [STORAGE_KEYS.presets]: presets });

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Preset saved:`, name);
      }

      return true;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error saving preset:`,
        error
      );
      throw error;
    }
  }

  /**
   * Load a specific preset
   * @param {string} name - Preset name
   * @returns {Promise<Object|null>} Preset settings or null if not found
   */
  static async loadPreset(name) {
    try {
      if (!name) {
        return null;
      }

      const presets = await this.getPresets();
      const preset = presets[name];

      return preset || null;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error loading preset:`,
        error
      );
      return null;
    }
  }

  /**
   * Delete a preset
   * @param {string} name - Preset name
   * @returns {Promise<boolean>} Success status
   */
  static async deletePreset(name) {
    try {
      if (!name) {
        throw new Error("Preset name is required");
      }

      const presets = await this.getPresets();

      if (!(name in presets)) {
        throw new Error(`Preset '${name}' not found`);
      }

      delete presets[name];
      await this.set({ [STORAGE_KEYS.presets]: presets });

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Preset deleted:`, name);
      }

      return true;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error deleting preset:`,
        error
      );
      throw error;
    }
  }

  /**
   * Get all preset names
   * @returns {Promise<string[]>} Array of preset names
   */
  static async getPresetNames() {
    try {
      const presets = await this.getPresets();
      return Object.keys(presets).sort();
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Error getting preset names:`,
        error
      );
      return [];
    }
  }
}
