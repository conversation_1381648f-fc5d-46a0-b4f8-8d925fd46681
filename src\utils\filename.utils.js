/**
 * Filename Utility Functions
 * Handles filename generation, validation, and placeholder replacement
 */

import { sanitizeFilename } from "./validation.utils.js";

/**
 * Replace placeholders in filename pattern with actual values
 * @param {string} pattern - Filename pattern with placeholders
 * @param {Object} context - Context object with replacement values
 * @returns {string} Processed filename with placeholders replaced
 */
export function replacePlaceholders(pattern, context = {}) {
  if (!pattern || typeof pattern !== "string") {
    return "image";
  }

  let result = pattern;

  // Get current date and time
  const now = new Date();

  // Format date as YYYY-MM-DD
  const date = now.toISOString().split("T")[0];

  // Format time as HH-MM-SS (replace colons to avoid filename issues)
  const time = now.toTimeString().split(" ")[0].replace(/:/g, "-");

  // Get domain and title from page context (passed from content script)
  const domain = context.pageContext?.domain || "unknown";
  const title = context.pageContext?.title || "untitled";

  // Define replacements
  const replacements = {
    "{date}": date,
    "{time}": time,
    "{domain}": domain,
    "{title}": title,
    "{index}": context.index || "1",
  };

  // Replace all placeholders
  Object.entries(replacements).forEach(([placeholder, value]) => {
    result = result.replace(
      new RegExp(placeholder.replace(/[{}]/g, "\\$&"), "g"),
      value
    );
  });

  return result;
}

/**
 * Generate filename with pattern and context
 * @param {string} pattern - Filename pattern
 * @param {Object} context - Context for replacements
 * @returns {string} Generated and sanitized filename
 */
export function generateFilename(pattern, context = {}) {
  const processedFilename = replacePlaceholders(pattern, context);
  return sanitizeFilename(processedFilename);
}

/**
 * Process filename for multiple files with index
 * @param {string} basePattern - Base filename pattern
 * @param {number} index - File index (1-based)
 * @param {number} total - Total number of files
 * @param {Object} pageContext - Page context from content script
 * @returns {string} Processed filename
 */
export function processFilenameWithIndex(
  basePattern,
  index,
  total,
  pageContext = null
) {
  const context = {
    index: total > 1 ? index : undefined,
    pageContext: pageContext,
  };

  return generateFilename(basePattern, context);
}

/**
 * Validate filename pattern
 * @param {string} pattern - Filename pattern to validate
 * @returns {Object} Validation result with valid flag and message
 */
export function validateFilenamePattern(pattern) {
  if (!pattern || typeof pattern !== "string") {
    return { valid: false, message: "Pattern cannot be empty" };
  }

  const trimmed = pattern.trim();

  if (trimmed.length === 0) {
    return { valid: false, message: "Pattern cannot be empty" };
  }

  // Check for invalid characters (after placeholder replacement)
  const testPattern = replacePlaceholders(trimmed, { index: 1 });
  const invalidChars = /[<>:"/\\|?*]/g;

  if (invalidChars.test(testPattern)) {
    return {
      valid: false,
      message: 'Pattern contains invalid characters: < > : " / \\ | ? *',
    };
  }

  return { valid: true, message: "Pattern is valid" };
}
