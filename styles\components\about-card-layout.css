/* ===================================================================
   ABOUT CARD LAYOUT COMPONENT STYLES
   Extracted from about.css for modular architecture
   ================================================================= */

/* Enhanced Card Row Layout */
.about-card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.about-card-row .about-card {
  margin-bottom: 0;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .about-card-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .about-card-row .about-card {
    margin-bottom: var(--spacing-lg);
  }
}

@media print {
  .about-card-row {
    grid-template-columns: 1fr;
  }
}
