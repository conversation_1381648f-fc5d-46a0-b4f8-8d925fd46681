/**
 * Cache Service
 * Provides intelligent caching for SVG conversions and data
 */

import { EXTENSION_CONFIG, DEBUG } from "../config/constants.js";

/**
 * Cache service for managing temporary data and conversion results
 */
export class CacheService {
  constructor(options = {}) {
    this.cache = new Map();

    // Apply default settings with option to override
    const defaults = {
      maxCacheSize: 50, // Maximum number of cached items
      maxAge: 5 * 60 * 1000, // 5 minutes in milliseconds
      cleanupInterval: 2 * 60 * 1000, // 2 minutes in milliseconds
    };

    // Merge defaults with provided options
    const config = { ...defaults, ...options };

    this.maxCacheSize = config.maxCacheSize;
    this.maxAge = config.maxAge;
    this.cleanupInterval = null;

    // Start cleanup scheduler with configured interval
    this._startCleanupScheduler(config.cleanupInterval);
  }

  /**
   * Store data in cache with expiration
   * @param {string} key - Cache key
   * @param {*} data - Data to cache
   * @param {number} maxAge - Custom max age in milliseconds (optional)
   */
  set(key, data, maxAge = null) {
    try {
      const expiry = Date.now() + (maxAge || this.maxAge);

      this.cache.set(key, {
        data,
        expiry,
        created: Date.now(),
        accessed: Date.now(),
        accessCount: 1,
      });

      // Cleanup if cache is too large
      this._enforceMaxSize();

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Cache set:`, key);
      }
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Cache set error:`, error);
    }
  }

  /**
   * Retrieve data from cache
   * @param {string} key - Cache key
   * @returns {*} Cached data or null if not found/expired
   */
  get(key) {
    try {
      const item = this.cache.get(key);

      if (!item) {
        return null;
      }

      // Check if expired
      if (Date.now() > item.expiry) {
        this.cache.delete(key);
        return null;
      }

      // Update access info
      item.accessed = Date.now();
      item.accessCount++;

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Cache hit:`, key);
      }

      return item.data;
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Cache get error:`, error);
      return null;
    }
  }

  /**
   * Check if key exists in cache and is not expired
   * @param {string} key - Cache key
   * @returns {boolean} Whether key exists and is valid
   */
  has(key) {
    try {
      const item = this.cache.get(key);

      if (!item) {
        return false;
      }

      // Check if expired
      if (Date.now() > item.expiry) {
        this.cache.delete(key);
        return false;
      }

      return true;
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Cache has error:`, error);
      return false;
    }
  }

  /**
   * Remove item from cache
   * @param {string} key - Cache key
   * @returns {boolean} Whether item was removed
   */
  delete(key) {
    try {
      const result = this.cache.delete(key);

      if (DEBUG.enabled && result) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Cache deleted:`, key);
      }

      return result;
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Cache delete error:`, error);
      return false;
    }
  }

  /**
   * Clear all cache entries
   */
  clear() {
    try {
      this.cache.clear();

      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Cache cleared`);
      }
    } catch (error) {
      console.error(`${EXTENSION_CONFIG.logPrefix} Cache clear error:`, error);
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getStats() {
    const stats = {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      entries: [],
    };

    this.cache.forEach((item, key) => {
      stats.entries.push({
        key,
        size: this._getItemSize(item.data),
        created: new Date(item.created).toISOString(),
        accessed: new Date(item.accessed).toISOString(),
        accessCount: item.accessCount,
        expired: Date.now() > item.expiry,
      });
    });

    return stats;
  }

  /**
   * Generate cache key for SVG data
   * @param {Element} svgElement - SVG element
   * @param {Object} settings - Conversion settings
   * @returns {string} Cache key
   */
  generateSVGKey(svgElement, settings = {}) {
    try {
      // Create a hash-like key from SVG content and settings
      const svgContent = svgElement.outerHTML.substring(0, 500); // Limit length
      const settingsStr = JSON.stringify(settings);
      const baseKey = `svg_${svgContent.length}_${settingsStr}`;

      // Simple hash function
      let hash = 0;
      for (let i = 0; i < baseKey.length; i++) {
        const char = baseKey.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      return `svg_${Math.abs(hash)}`;
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Cache key generation error:`,
        error
      );
      return `svg_${Date.now()}_${Math.random()}`;
    }
  }

  /**
   * Cache SVG conversion result
   * @param {Element} svgElement - SVG element
   * @param {Object} settings - Conversion settings
   * @param {*} result - Conversion result
   */
  cacheConversion(svgElement, settings, result) {
    const key = this.generateSVGKey(svgElement, settings);
    this.set(key, result, 10 * 60 * 1000); // Cache for 10 minutes
  }

  /**
   * Get cached conversion result
   * @param {Element} svgElement - SVG element
   * @param {Object} settings - Conversion settings
   * @returns {*} Cached result or null
   */
  getCachedConversion(svgElement, settings) {
    const key = this.generateSVGKey(svgElement, settings);
    return this.get(key);
  }

  /**
   * Enforce maximum cache size by removing least recently used items
   * @private
   */
  _enforceMaxSize() {
    if (this.cache.size <= this.maxCacheSize) {
      return;
    }

    // Sort by last accessed time (LRU)
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].accessed - b[1].accessed);

    // Remove oldest entries
    const toRemove = entries.slice(0, this.cache.size - this.maxCacheSize);
    toRemove.forEach(([key]) => this.cache.delete(key));

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Cache size enforced, removed ${toRemove.length} items`
      );
    }
  }

  /**
   * Clean up expired entries
   * @private
   */
  _cleanupExpired() {
    const now = Date.now();
    let removedCount = 0;

    this.cache.forEach((item, key) => {
      if (now > item.expiry) {
        this.cache.delete(key);
        removedCount++;
      }
    });

    if (DEBUG.enabled && removedCount > 0) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Cache cleanup removed ${removedCount} expired items`
      );
    }
  }

  /**
   * Start periodic cleanup scheduler
   * @param {number} interval - Cleanup interval in milliseconds
   * @private
   */
  _startCleanupScheduler(interval) {
    // Cleanup expired items periodically
    this.cleanupInterval = setInterval(() => {
      this._cleanupExpired();
    }, interval);
  }

  /**
   * Get approximate size of cached item
   * @param {*} data - Data to measure
   * @returns {number} Approximate size in bytes
   * @private
   */
  _getItemSize(data) {
    try {
      if (typeof data === "string") {
        return data.length * 2; // Approximate UTF-16 encoding
      } else if (data instanceof ArrayBuffer) {
        return data.byteLength;
      } else if (data instanceof Blob) {
        return data.size;
      } else {
        return JSON.stringify(data).length * 2;
      }
    } catch (error) {
      return 0;
    }
  }

  /**
   * Clean up cache service
   */
  cleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Create singleton instance
let cacheServiceInstance = null;

/**
 * Get cache service singleton
 * @returns {CacheService} Cache service instance
 */
export function getCacheService() {
  if (!cacheServiceInstance) {
    cacheServiceInstance = new CacheService();
  }
  return cacheServiceInstance;
}
