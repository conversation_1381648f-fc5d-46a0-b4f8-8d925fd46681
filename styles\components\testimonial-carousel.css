/* ===================================================================
   TESTIMONIAL CAROUSEL COMPONENT STYLES
   Extracted from about.css for modular architecture
   ================================================================= */

/* Enhanced Testimonial Carousel with Advanced Swap Animations */
.testimonial-carousel {
  position: relative;
  min-height: 180px;
  overflow: hidden;
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%
  );
  padding: var(--spacing-lg);
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Enhanced Testimonial States with 3D Transformations */
.testimonial {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: var(--spacing-lg);
  opacity: 0;
  transform: translateX(100%) rotateY(45deg) scale(0.8) translateZ(-50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  backface-visibility: hidden;
  will-change: transform, opacity;
  filter: blur(3px) brightness(0.7);
}

.testimonial.active {
  opacity: 1;
  transform: translateX(0) rotateY(0deg) scale(1);
  filter: blur(0px) brightness(1);
  z-index: 10;
}

.testimonial.entering {
  animation: testimonialEnter 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.testimonial.exiting {
  animation: testimonialExit 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.testimonial.slide-left {
  transform: translateX(-100%) rotateY(-45deg) scale(0.8);
}

.testimonial.slide-right {
  transform: translateX(100%) rotateY(45deg) scale(0.8);
}

/* Advanced Keyframe Animations */
@keyframes testimonialEnter {
  0% {
    opacity: 0;
    transform: translateX(100%) rotateY(45deg) scale(0.8) translateZ(-50px);
    filter: blur(8px) brightness(0.5);
  }
  25% {
    opacity: 0.3;
    transform: translateX(75%) rotateY(30deg) scale(0.85) translateZ(-30px);
    filter: blur(6px) brightness(0.6);
  }
  50% {
    opacity: 0.6;
    transform: translateX(50%) rotateY(15deg) scale(0.9) translateZ(-15px);
    filter: blur(4px) brightness(0.75);
  }
  75% {
    opacity: 0.8;
    transform: translateX(25%) rotateY(5deg) scale(0.95) translateZ(-5px);
    filter: blur(2px) brightness(0.9);
  }
  100% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg) scale(1) translateZ(0px);
    filter: blur(0px) brightness(1);
  }
}

@keyframes testimonialExit {
  0% {
    opacity: 1;
    transform: translateX(0) rotateY(0deg) scale(1) translateZ(0px);
    filter: blur(0px) brightness(1);
  }
  25% {
    opacity: 0.8;
    transform: translateX(-25%) rotateY(-5deg) scale(0.95) translateZ(-5px);
    filter: blur(2px) brightness(0.9);
  }
  50% {
    opacity: 0.6;
    transform: translateX(-50%) rotateY(-15deg) scale(0.9) translateZ(-15px);
    filter: blur(4px) brightness(0.75);
  }
  75% {
    opacity: 0.3;
    transform: translateX(-75%) rotateY(-30deg) scale(0.85) translateZ(-30px);
    filter: blur(6px) brightness(0.6);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%) rotateY(-45deg) scale(0.8) translateZ(-50px);
    filter: blur(8px) brightness(0.5);
  }
}

/* Fade and Scale Animation Variants */
@keyframes testimonialFadeSlide {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

@keyframes testimonialFlip {
  0% {
    opacity: 0;
    transform: rotateX(90deg) scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: rotateX(45deg) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: rotateX(0deg) scale(1);
  }
}

/* Enhanced Profile Elements */
.testimonial-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.6);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(5px);
  transition: all 0.4s ease;
  transform: translateY(10px);
  opacity: 0;
}

.testimonial.active .testimonial-profile {
  animation: profileSlideIn 0.6s ease 0.2s forwards;
}

@keyframes profileSlideIn {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.profile-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.testimonial.active .profile-avatar {
  animation: avatarBounce 0.6s ease 0.4s;
}

@keyframes avatarBounce {
  0%,
  100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1) rotate(5deg);
  }
  50% {
    transform: scale(1.05) rotate(-3deg);
  }
  75% {
    transform: scale(1.08) rotate(2deg);
  }
}

.profile-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: avatar-shine 3s ease-in-out infinite;
  opacity: 0;
}

.testimonial.active .profile-avatar::before {
  opacity: 1;
}

@keyframes avatar-shine {
  0%,
  100% {
    transform: rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: rotate(180deg);
    opacity: 1;
  }
}

/* Enhanced Text Elements */
.testimonial-text {
  font-style: italic;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-md);
  font-size: 1.05rem;
  line-height: 1.6;
  position: relative;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.6s ease;
}

.testimonial.active .testimonial-text {
  animation: textReveal 0.8s ease 0.3s forwards;
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    opacity: 1;
  }
}

.testimonial-text::before,
.testimonial-text::after {
  content: '"';
  font-size: 2rem;
  color: var(--primary-color);
  opacity: 0.3;
  position: absolute;
  font-family: serif;
  transition: all 0.4s ease;
}

.testimonial.active .testimonial-text::before,
.testimonial.active .testimonial-text::after {
  animation: quoteGlow 1s ease 0.5s;
}

@keyframes quoteGlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
    text-shadow: 0 0 10px var(--primary-color);
  }
}

.testimonial-text::before {
  top: -10px;
  left: -15px;
}

.testimonial-text::after {
  bottom: -25px;
  right: -10px;
}

/* Enhanced Meta Information */
.testimonial-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(226, 232, 240, 0.4);
  font-size: 0.8rem;
  transform: translateY(15px);
  opacity: 0;
}

.testimonial.active .testimonial-meta {
  animation: metaSlideUp 0.6s ease 0.6s forwards;
}

@keyframes metaSlideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.profile-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.profile-role {
  color: var(--text-secondary);
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.testimonial.active .verified-badge {
  animation: badgePop 0.4s ease 0.8s forwards;
}

@keyframes badgePop {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: scale(1);
  }
}

/* Enhanced Navigation Dots */
.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 120, 212, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border: 2px solid transparent;
  overflow: hidden;
}

.testimonial-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(0, 120, 212, 0.2);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  border-radius: 50%;
}

.testimonial-dot:hover {
  background: rgba(0, 120, 212, 0.6);
  transform: scale(1.3);
  box-shadow: 0 0 15px rgba(0, 120, 212, 0.4);
}

.testimonial-dot:hover::before {
  width: 20px;
  height: 20px;
  opacity: 0.3;
}

.testimonial-dot.active {
  background: var(--primary-color);
  transform: scale(1.4);
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.5);
}

.testimonial-dot.active::before {
  width: 25px;
  height: 25px;
  opacity: 0.2;
  animation: dotPulse 2s ease-in-out infinite;
}

@keyframes dotPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.2;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.1;
  }
}

/* Enhanced Carousel Controls */
.carousel-controls {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 20;
  padding: 0 var(--spacing-md);
}

.carousel-btn {
  background: rgba(0, 120, 212, 0.9);
  color: white;
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
  transition: all 0.4s ease;
  opacity: 0;
  transform: scale(0.7) translateY(10px);
  position: relative;
  overflow: hidden;
}

.carousel-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.testimonial-carousel:hover .carousel-btn {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.carousel-btn:hover {
  background: var(--primary-color);
  transform: scale(1.15) translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 120, 212, 0.4);
}

.carousel-btn:hover::before {
  left: 100%;
}

.carousel-btn:active {
  transform: scale(1.05) translateY(0);
}

/* Enhanced Progress Bar */
.carousel-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 120, 212, 0.2);
  overflow: hidden;
}

.carousel-progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--primary-color) 0%,
    rgba(0, 120, 212, 0.8) 100%
  );
  width: 0%;
  transition: width 0.3s ease;
  position: relative;
}

.carousel-progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Testimonial Card Hover Effects */
.testimonial-card {
  transition: all 0.4s ease;
}

.testimonial-card:hover .testimonial-carousel {
  transform: scale(1.02);
}

.testimonial-card:hover .testimonial.active {
  transform: translateX(0) rotateY(0deg) scale(1.02);
}

/* Animation Variants for Different Directions */
.testimonial.slide-from-right {
  transform: translateX(100%) rotateY(45deg) scale(0.8);
}

.testimonial.slide-from-left {
  transform: translateX(-100%) rotateY(-45deg) scale(0.8);
}

.testimonial.slide-from-top {
  transform: translateY(-50px) rotateX(45deg) scale(0.8);
}

.testimonial.slide-from-bottom {
  transform: translateY(50px) rotateX(-45deg) scale(0.8);
}

/* Performance Optimizations */
.testimonial-carousel,
.testimonial,
.carousel-btn,
.testimonial-dot {
  will-change: transform, opacity;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .testimonial,
  .testimonial-profile,
  .testimonial-text,
  .testimonial-meta,
  .verified-badge,
  .carousel-btn,
  .testimonial-dot {
    animation: none !important;
    transition: opacity 0.3s ease !important;
  }

  .testimonial {
    transform: none !important;
  }

  .testimonial:not(.active) {
    opacity: 0;
  }

  .testimonial.active {
    opacity: 1;
  }
}

/* Focus States for Accessibility */
.testimonial-dot:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 120, 212, 0.2);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .testimonial-nav {
    display: none;
  }
}
