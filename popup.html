<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="styles/popup.css" />
  </head>
  <body>
    <div class="header">
      <h2>SVG Switcher</h2>
      <button
        id="settingsBtn"
        class="settings-btn"
        title="Open Settings"
        aria-label="Open Settings"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path
            d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"
          ></path>
          <circle cx="12" cy="12" r="3"></circle>
        </svg>
      </button>
    </div>

    <div id="status" class="status no-selection">No SVG selected</div>

    <div id="svgInfo" class="svg-info hidden">
      <div id="svgDetails"></div>
    </div>

    <div class="batch-controls hidden" id="batchControls">
      <label>Download Options:</label>
      <div class="batch-options">
        <label>
          <input type="radio" name="downloadType" value="separate" checked />
          Separate Files
        </label>
        <label>
          <input type="radio" name="downloadType" value="zip" />
          ZIP Archive
        </label>
      </div>
      <div class="progress-bar" id="progressBar">
        <div class="progress-bar-fill" id="progressBarFill"></div>
      </div>
    </div>

    <div class="controls">
      <input
        type="text"
        id="filename"
        class="filename-input form-control"
        placeholder="Enter filename..."
        value="image"
      />

      <div class="quality-selector quality-control">
        <label for="quality">Quality:</label>
        <select id="quality" class="form-control">
          <option value="1" selected>Original Size (1x)</option>
          <option value="2">High Quality (2x)</option>
          <option value="3">Ultra Quality (3x)</option>
          <option value="4">Maximum Quality (4x)</option>
        </select>
      </div>

      <div class="dimension-controls custom-dimensions-control">
        <label>Custom Size (px):</label>
        <div>
          <input
            type="number"
            id="customWidth"
            min="1"
            placeholder="Width"
            class="form-control"
          />
          <input
            type="number"
            id="customHeight"
            min="1"
            placeholder="Height"
            class="form-control"
          />
        </div>
      </div>

      <div class="background-controls background-control">
        <label for="bgColor">Background Color:</label>
        <input type="color" id="bgColor" value="#ffffff" />
        <label class="transparent-bg-control">
          <input type="checkbox" id="transparentBg" />
          Transparent
        </label>
      </div>

      <div class="format-controls">
        <label for="format">Format:</label>
        <select id="format" class="form-control">
          <option value="png" selected>PNG</option>
          <option value="jpeg">JPEG</option>
          <option value="webp">WebP</option>
          <option value="svg">SVG (Original)</option>
        </select>
      </div>

      <div class="presets-controls">
        <label>Presets:</label>
        <div>
          <select id="presetSelect" class="form-control">
            <option value="">Select a preset...</option>
          </select>
          <button id="deletePresetBtn" disabled>Delete</button>
        </div>
        <div>
          <input
            type="text"
            id="presetName"
            placeholder="Preset name..."
            class="form-control"
          />
          <button id="savePresetBtn">Save</button>
        </div>
      </div>

      <div class="action-buttons">
        <button id="downloadBtn" class="download-btn" disabled>Download</button>
        <button
          id="copyToClipboardBtn"
          class="clipboard-btn"
          disabled
          title="Copy rasterized SVG to clipboard (first SVG if multiple selected)"
        >
          Copy to Clipboard
        </button>
      </div>
    </div>

    <div id="previewContainer" class="preview-container">
      <label>Preview:</label>
      <div>
        <canvas id="previewCanvas"></canvas>
      </div>
    </div>

    <div class="instructions">
      Use Ctrl+Click (or Cmd+Click on Mac) to select SVG images. Press Escape to
      clear selections.
    </div>

    <script src="lib/jszip.min.js"></script>
    <script type="module" src="entries/popup.js"></script>
  </body>
</html>
