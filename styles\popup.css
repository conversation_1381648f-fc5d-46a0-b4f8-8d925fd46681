@import url("variables.css");

/* Utility Classes */
.hidden {
  display: none !important;
}

.error {
  background-color: var(--bg-error, #fef2f2);
  border: 1px solid var(--border-error, #fecaca);
  color: var(--text-error, #dc2626);
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus Indicators */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .status.selected {
    border-width: 2px;
  }

  .download-btn {
    border: 2px solid var(--primary-color);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Popup Main Styles */
body {
  width: 320px;
  padding: var(--spacing-xl);
  font-family: var(--font-family);
  margin: 0;
  background: linear-gradient(135deg, #f0f4ff, #ffffff);
  box-shadow: var(--shadow-sm);
  border-radius: var(--border-radius-xl);
  color: var(--text-primary);
}

/* Header Section */
.header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.header h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: var(--font-size-xl);
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

/* Settings Button */
.settings-btn {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  color: var(--secondary-color);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 5px transparent;
}

.settings-btn:hover {
  color: var(--primary-hover);
  background-color: var(--bg-light);
  box-shadow: 0 0 8px var(--primary-color);
}

.settings-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  box-shadow: 0 0 8px var(--primary-color);
}

.settings-btn svg {
  width: 18px;
  height: 18px;
}

/* Status Display */
.status {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  font-size: var(--font-size-md);
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  transition: background-color var(--transition-fast);
}

.status.no-selection {
  background-color: #fff7e6;
  border: 1px solid #ffecb3;
  color: #b36b00;
}

.status.selected {
  background-color: #e6f0ff;
  border: 1px solid #99c2ff;
  color: #004080;
}

/* SVG Information Display */
.svg-info {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

/* Controls Container */
.controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Common Form Control Styles */
.form-control {
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-md);
  background-color: var(--bg-primary);
  transition: border-color var(--transition-fast),
    box-shadow var(--transition-fast);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 8px var(--primary-color);
  outline: none;
}

/* Input Fields (now using .form-control) */

/* Action Buttons Container */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-direction: row;
  justify-content: center;
}

/* Buttons */
.download-btn,
.clipboard-btn {
  padding: 12px var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  font-size: var(--font-size-md);
  font-weight: 600;
  transition: background-color var(--transition-fast),
    box-shadow var(--transition-fast);
  box-shadow: 0 4px 8px rgba(0, 120, 212, 0.3);
  color: var(--bg-primary);
  user-select: none;
  min-width: 120px;
}

.download-btn {
  background-color: var(--primary-color);
}

.download-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
  box-shadow: 0 6px 12px rgba(16, 110, 190, 0.6);
}

.clipboard-btn {
  background-color: var(--secondary-color);
}

.clipboard-btn:hover:not(:disabled) {
  background-color: var(--secondary-hover);
  box-shadow: 0 6px 12px rgba(90, 98, 104, 0.6);
}

.download-btn:disabled,
.clipboard-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

/* Instructions Text */
.instructions {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  margin-top: var(--spacing-lg);
  line-height: 1.5;
  font-style: italic;
}

/* Quality Selector */
.quality-selector {
  margin-bottom: var(--spacing-md);
}

.quality-selector label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.quality-selector select {
  width: 100%;
  border-radius: var(--border-radius-lg);
}

/* Batch Controls */
.batch-controls {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

.batch-controls label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.batch-options {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.batch-options label {
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

/* Progress Bar */
.progress-bar {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  margin-top: var(--spacing-md);
  overflow: hidden;
  display: none;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color);
  width: 0%;
  transition: width var(--transition-medium);
}

/* Dimension Controls */
.dimension-controls {
  margin-bottom: var(--spacing-md);
}

.dimension-controls label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 600;
}

.dimension-controls > div {
  display: flex;
  gap: var(--spacing-sm);
}

.dimension-controls input {
  width: 70px;
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-lg);
}

/* Background Controls */
.background-controls {
  margin-bottom: var(--spacing-md);
}

.background-controls label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 600;
}

.background-controls input[type="color"] {
  width: 40px;
  height: 28px;
  vertical-align: middle;
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: box-shadow var(--transition-fast);
}

.background-controls input[type="color"]:focus {
  box-shadow: 0 0 8px var(--primary-color);
  outline: none;
}

.background-controls input[type="checkbox"] {
  vertical-align: middle;
  cursor: pointer;
}

/* Format Controls */
.format-controls {
  margin-bottom: var(--spacing-md);
}

.format-controls label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 600;
}

.format-controls select {
  width: 100%;
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-lg);
}

/* Presets Controls */
.presets-controls {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--bg-light);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
}

.presets-controls label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
}

.presets-controls > div:first-of-type {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.presets-controls > div:last-of-type {
  display: flex;
  gap: var(--spacing-sm);
}

.presets-controls select,
.presets-controls input {
  flex: 1;
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-lg);
}

.presets-controls button {
  padding: var(--spacing-sm) 12px;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 600;
  transition: background-color var(--transition-fast),
    box-shadow var(--transition-fast);
  user-select: none;
}

.presets-controls button:first-of-type {
  background-color: var(--danger-color);
  color: var(--bg-primary);
}

.presets-controls button:first-of-type:hover {
  background-color: #b02a37;
  box-shadow: 0 0 8px #b02a37;
}

.presets-controls button:last-of-type {
  background-color: var(--success-color);
  color: var(--bg-primary);
}

.presets-controls button:last-of-type:hover {
  background-color: #218838;
  box-shadow: 0 0 8px #218838;
}

/* Preview Container */
.preview-container {
  margin: 18px 0 0 0;
  text-align: center;
  display: block;
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.preview-container label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 600;
}

.preview-container > div {
  margin-top: var(--spacing-sm);
}

.preview-container canvas {
  border: 1px solid #eee;
  border-radius: var(--border-radius-sm);
  max-width: 100%;
  background: #fafafa;
}
