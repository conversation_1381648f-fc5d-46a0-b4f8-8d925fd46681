/**
 * SVG Switcher Extension - Popup Entry Point
 * Simplified main entry point using modular controller architecture
 */

import { PopupController } from "../src/popup/popup-controller.js";
import { EXTENSION_CONFIG, DEBUG } from "../src/config/constants.js";
import { initializeAccessibility } from "../src/utils/accessibility.utils.js";
import { preloadCriticalModules } from "../src/utils/lazy-loader.utils.js";

/**
 * Main popup initialization
 */
document.addEventListener("DOMContentLoaded", async () => {
  try {
    if (DEBUG.enabled) {
      console.log(`${EXTENSION_CONFIG.logPrefix} Popup initializing...`);
    }

    // Preload critical modules for better performance
    try {
      await preloadCriticalModules();
      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Critical modules preloaded`);
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to preload modules:`,
        error
      );
      throw new Error(`Module preload failed: ${error.message}`);
    }

    // Initialize accessibility features
    try {
      initializeAccessibility(document);
      if (DEBUG.enabled) {
        console.log(`${EXTENSION_CONFIG.logPrefix} Accessibility initialized`);
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to initialize accessibility:`,
        error
      );
      // Don't throw here - accessibility is important but not critical for basic functionality
    }

    // Initialize the popup controller
    try {
      const popupController = new PopupController();
      await popupController.initialize();
      if (DEBUG.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} PopupController initialized`
        );
      }

      // Expose controller for debugging in development
      if (DEBUG.enabled) {
        window.svg2pngPopup = popupController;
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to initialize PopupController:`,
        error
      );
      throw new Error(
        `PopupController initialization failed: ${error.message}`
      );
    }

    if (DEBUG.enabled) {
      console.log(
        `${EXTENSION_CONFIG.logPrefix} Popup initialized successfully`
      );
    }
  } catch (error) {
    console.error(
      `${EXTENSION_CONFIG.logPrefix} Popup initialization error:`,
      error
    );

    // Show user-friendly error message with more details in debug mode
    const status = document.getElementById("status");
    if (status) {
      status.className = "status error";
      if (DEBUG.enabled) {
        status.textContent = `Error: ${error.message}. Check console for details.`;
      } else {
        status.textContent =
          "Error initializing popup. Please try closing and reopening.";
      }
    }
  }
});

/**
 * Handle popup cleanup on window unload
 */
window.addEventListener("beforeunload", () => {
  if (DEBUG.enabled) {
    console.log(`${EXTENSION_CONFIG.logPrefix} Popup unloading...`);
  }

  // Cleanup can be added here if needed
});
