/**
 * Style Manager for Content Script
 * Handles CSS injection and dynamic styling
 */

import { CSS_CLASSES, EXTENSION_CONFIG } from "../config/constants.js";

/**
 * Manages CSS styles and injections for the content script
 */
export class StyleManager {
  constructor() {
    this.styleId = "svg-switcher-styles";
    this.injected = false;
  }

  /**
   * Inject CSS styles for SVG selection and toolbar
   */
  injectStyles() {
    // Remove existing styles if present
    this.removeStyles();

    const style = document.createElement("style");
    style.id = this.styleId;
    style.textContent = this._getStylesheetContent();

    document.head.appendChild(style);
    this.injected = true;
  }

  /**
   * Remove injected styles
   */
  removeStyles() {
    const existingStyle = document.getElementById(this.styleId);
    if (existingStyle) {
      existingStyle.remove();
      this.injected = false;
    }
  }

  /**
   * Check if styles are injected
   * @returns {boolean} Whether styles are currently injected
   */
  areStylesInjected() {
    return this.injected && document.getElementById(this.styleId) !== null;
  }

  /**
   * Get the complete stylesheet content
   * @returns {string} CSS stylesheet content
   * @private
   */
  _getStylesheetContent() {
    return `
      /* SVG Selection Styles */
      .${CSS_CLASSES.selected} {
        outline: 2px dashed #0078d4 !important;
        outline-offset: 2px !important;
      }
      
      /* Toolbar Styles */
      .${CSS_CLASSES.toolbar} {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ffffff;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        z-index: 10000;
        animation: svg2png-slideIn 0.3s ease;
        user-select: none;
        cursor: move;
        min-width: 200px;
      }
      
      .${CSS_CLASSES.toolbarHeader} {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #e1e5e9;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
        cursor: move;
      }
      
      .${CSS_CLASSES.toolbarBadge} {
        background: #0078d4;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }
      
      .${CSS_CLASSES.toolbarClose} {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;
        line-height: 1;
      }
      
      .${CSS_CLASSES.toolbarClose}:hover {
        background: #e1e5e9;
        color: #333;
      }
      
      .${CSS_CLASSES.toolbarContent} {
        padding: 12px 16px;
        display: flex;
        gap: 8px;
      }
      
      .${CSS_CLASSES.toolbarBtn} {
        background: #0078d4;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: background 0.2s ease;
      }
      
      .${CSS_CLASSES.toolbarBtn}:hover {
        background: #106ebe;
      }
      
      .${CSS_CLASSES.toolbarBtn}.secondary {
        background: #6c757d;
      }
      
      .${CSS_CLASSES.toolbarBtn}.secondary:hover {
        background: #5a6268;
      }
      
      /* Animations */
      @keyframes svg2png-slideIn {
        from { 
          opacity: 0; 
          transform: translate(20px, -10px) scale(0.95); 
        }
        to { 
          opacity: 1; 
          transform: translate(0, 0) scale(1); 
        }
      }
      
      @keyframes svg2png-slideOut {
        from { 
          opacity: 1; 
          transform: scale(1); 
        }
        to { 
          opacity: 0; 
          transform: scale(0.95); 
        }
      }
      
      .${CSS_CLASSES.toolbar}.${CSS_CLASSES.closing} {
        animation: svg2png-slideOut 0.2s ease forwards;
      }
      
      /* Accessibility enhancements */
      .${CSS_CLASSES.toolbar}:focus-within {
        outline: 2px solid #0078d4;
        outline-offset: 2px;
      }
      
      .${CSS_CLASSES.toolbarBtn}:focus {
        outline: 2px solid #ffffff;
        outline-offset: 2px;
      }
      
      /* High contrast mode support */
      @media (prefers-contrast: high) {
        .${CSS_CLASSES.selected} {
          outline-color: HighlightText !important;
          outline-width: 3px !important;
        }
        
        .${CSS_CLASSES.toolbar} {
          border: 2px solid ButtonText;
        }
      }
      
      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        .${CSS_CLASSES.toolbar} {
          animation: none;
        }
        
        .${CSS_CLASSES.toolbar}.${CSS_CLASSES.closing} {
          animation: none;
          opacity: 0;
        }
        
        .${CSS_CLASSES.toolbarBtn} {
          transition: none;
        }
      }
    `;
  }

  /**
   * Update toolbar position
   * @param {HTMLElement} toolbar - Toolbar element
   * @param {number} x - X coordinate
   * @param {number} y - Y coordinate
   */
  updateToolbarPosition(toolbar, x, y) {
    if (!toolbar) return;

    // Ensure toolbar stays within viewport
    const rect = toolbar.getBoundingClientRect();
    const maxX = window.innerWidth - rect.width;
    const maxY = window.innerHeight - rect.height;

    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));

    toolbar.style.left = `${constrainedX}px`;
    toolbar.style.top = `${constrainedY}px`;
    toolbar.style.right = "auto"; // Remove right positioning when dragged
  }

  /**
   * Add closing animation class
   * @param {HTMLElement} element - Element to animate
   */
  addClosingAnimation(element) {
    if (element) {
      element.classList.add(CSS_CLASSES.closing);
    }
  }

  /**
   * Clean up all injected styles and classes
   */
  cleanup() {
    this.removeStyles();

    // Remove selection classes from all elements
    const selectedElements = document.querySelectorAll(
      `.${CSS_CLASSES.selected}`
    );
    selectedElements.forEach((element) => {
      element.classList.remove(CSS_CLASSES.selected);
    });
  }
}
