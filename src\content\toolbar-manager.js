/**
 * Toolbar Manager for Content Script
 * Handles toolbar creation, updating, and user interaction
 */

import {
  CSS_CLASSES,
  EXTENSION_CONFIG,
  ANIMATION,
} from "../config/constants.js";
import { StorageService } from "../services/storage.service.js";
import { announceToScreenReader } from "../utils/accessibility.utils.js";

/**
 * Manages the floating toolbar for SVG selection
 */
export class ToolbarManager {
  constructor(stateManager, styleManager) {
    this.stateManager = stateManager;
    this.styleManager = styleManager;
    this.toolbar = null;
    this.dragHandlers = {
      start: null,
      move: null,
      end: null,
    };
    this.autoHideTimeout = null;
    this.settings = {
      showToolbarOnSelection: true,
      autoHideToolbar: false,
      toolbarPosition: "top-right",
      rememberToolbarPosition: false,
    };

    // Load settings and set up storage listener
    this.loadSettings();
    this.setupStorageListener();
  }

  /**
   * Load settings from storage
   */
  async loadSettings() {
    try {
      const stored = await StorageService.get(["settings"], "sync");
      if (stored.settings) {
        this.settings = {
          showToolbarOnSelection:
            stored.settings.showToolbarOnSelection !== false,
          autoHideToolbar: stored.settings.autoHideToolbar || false,
          toolbarPosition: stored.settings.toolbarPosition || "top-right",
          rememberToolbarPosition:
            stored.settings.rememberToolbarPosition || false,
        };
      }

      if (EXTENSION_CONFIG.debug?.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Toolbar settings loaded:`,
          this.settings
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to load toolbar settings:`,
        error
      );
      // Use defaults if loading fails
    }
  }

  /**
   * Set up storage listener to reload settings when they change
   */
  setupStorageListener() {
    if (chrome.storage && chrome.storage.onChanged) {
      this.storageListener = (changes, areaName) => {
        if (areaName === "sync" && changes.settings) {
          this.loadSettings();
          if (EXTENSION_CONFIG.debug?.enabled) {
            console.log(
              `${EXTENSION_CONFIG.logPrefix} Toolbar settings updated from storage`
            );
          }
        }
      };
      chrome.storage.onChanged.addListener(this.storageListener);
    }
  }

  /**
   * Get toolbar position coordinates based on setting
   * @private
   */
  _getDefaultPosition() {
    const padding = 20;
    const positions = {
      "top-right": { top: padding, right: padding },
      "top-left": { top: padding, left: padding },
      "bottom-right": { bottom: padding, right: padding },
      "bottom-left": { bottom: padding, left: padding },
    };
    return positions[this.settings.toolbarPosition] || positions["top-right"];
  }

  /**
   * Apply position styles to toolbar
   * @private
   */
  async _applyPosition() {
    if (!this.toolbar) return;

    // Try to use saved position if rememberToolbarPosition is enabled
    if (this.settings.rememberToolbarPosition) {
      try {
        const stored = await StorageService.get(["settings"], "sync");
        if (stored.settings?.savedToolbarPosition) {
          const savedPos = stored.settings.savedToolbarPosition;
          this.toolbar.style.position = "fixed";
          this.toolbar.style.top = savedPos.top + "px";
          this.toolbar.style.left = savedPos.left + "px";
          this.toolbar.style.right = "auto";
          this.toolbar.style.bottom = "auto";
          return;
        }
      } catch (error) {
        console.error(
          `${EXTENSION_CONFIG.logPrefix} Failed to load saved position:`,
          error
        );
      }
    }

    // Use default position
    const position = this._getDefaultPosition();

    // Reset all position properties
    this.toolbar.style.top = "auto";
    this.toolbar.style.right = "auto";
    this.toolbar.style.bottom = "auto";
    this.toolbar.style.left = "auto";

    // Apply the chosen position
    Object.keys(position).forEach((prop) => {
      this.toolbar.style[prop] = position[prop] + "px";
    });
  }

  /**
   * Start auto-hide timer if enabled
   * @private
   */
  _startAutoHideTimer() {
    if (!this.settings.autoHideToolbar) return;

    this._clearAutoHideTimer();
    this.autoHideTimeout = setTimeout(() => {
      this.hideToolbar();
    }, 10000); // 10 seconds
  }

  /**
   * Clear auto-hide timer
   * @private
   */
  _clearAutoHideTimer() {
    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
      this.autoHideTimeout = null;
    }
  }

  /**
   * Create and display the selection toolbar
   * @returns {HTMLElement} Created toolbar element
   */
  async createToolbar() {
    // Check if toolbar should be shown based on settings
    if (!this.settings.showToolbarOnSelection) {
      return null;
    }

    if (this.toolbar) {
      this.removeToolbar();
    }

    this.toolbar = document.createElement("div");
    this.toolbar.className = CSS_CLASSES.toolbar;
    this.toolbar.setAttribute("role", "toolbar");
    this.toolbar.setAttribute("aria-label", "SVG selection tools");

    const header = this._createToolbarHeader();
    const content = this._createToolbarContent();

    this.toolbar.appendChild(header);
    this.toolbar.appendChild(content);

    document.body.appendChild(this.toolbar);
    await this._applyPosition();
    this._setupDragHandlers();
    this._startAutoHideTimer();

    this.stateManager.setToolbar(this.toolbar);
    return this.toolbar;
  }

  /**
   * Update toolbar content with current selection count
   */
  async updateToolbar() {
    const count = this.stateManager.getSelectionCount();

    // If no selections, hide toolbar
    if (count === 0) {
      this.hideToolbar();
      return;
    }

    // If toolbar should be shown and doesn't exist, create it
    if (this.settings.showToolbarOnSelection && !this.toolbar) {
      await this.createToolbar();
      return;
    }

    // Update existing toolbar
    if (!this.toolbar) return;

    const badge = this.toolbar.querySelector(`.${CSS_CLASSES.toolbarBadge}`);
    if (badge) {
      badge.textContent = count.toString();
      badge.setAttribute(
        "aria-label",
        `${count} SVG${count !== 1 ? "s" : ""} selected`
      );
    }

    // Announce selection changes
    announceToScreenReader(`${count} SVG${count !== 1 ? "s" : ""} selected`);

    // Reset auto-hide timer on activity
    this._startAutoHideTimer();

    // Show toolbar if hidden
    if (!this.isVisible()) {
      this.showToolbar();
    }
  }

  /**
   * Hide toolbar with animation
   */
  hideToolbar() {
    if (!this.toolbar) return;

    this._clearAutoHideTimer();
    this.styleManager.addClosingAnimation(this.toolbar);

    setTimeout(() => {
      this.removeToolbar();
    }, ANIMATION.slideOut);
  }

  /**
   * Show toolbar (remove hidden state)
   */
  showToolbar() {
    if (this.toolbar) {
      this.toolbar.style.display = "block";
    }
  }

  /**
   * Remove toolbar from DOM
   */
  removeToolbar() {
    if (this.toolbar) {
      this._removeDragHandlers();
      this.toolbar.remove();
      this.toolbar = null;
      this.stateManager.setToolbar(null);
    }
  }

  /**
   * Check if toolbar is visible
   * @returns {boolean} Whether toolbar is currently visible
   */
  isVisible() {
    return this.toolbar && this.toolbar.style.display !== "none";
  }

  /**
   * Get toolbar element
   * @returns {HTMLElement|null} Toolbar element
   */
  getToolbar() {
    return this.toolbar;
  }

  /**
   * Create toolbar header with title and controls
   * @returns {HTMLElement} Header element
   * @private
   */
  _createToolbarHeader() {
    const header = document.createElement("div");
    header.className = CSS_CLASSES.toolbarHeader;

    const title = document.createElement("span");
    title.textContent = "SVG Selection";
    title.style.fontWeight = "500";

    const badge = document.createElement("span");
    badge.className = CSS_CLASSES.toolbarBadge;
    badge.textContent = this.stateManager.getSelectionCount().toString();
    badge.setAttribute("aria-label", "Selected count");

    const closeBtn = document.createElement("button");
    closeBtn.className = CSS_CLASSES.toolbarClose;
    closeBtn.innerHTML = "×";
    closeBtn.setAttribute("aria-label", "Close toolbar and clear selections");
    closeBtn.addEventListener("click", () => this._handleCloseClick());

    header.appendChild(title);
    header.appendChild(badge);
    header.appendChild(closeBtn);

    return header;
  }

  /**
   * Create toolbar content with action buttons
   * @returns {HTMLElement} Content element
   * @private
   */
  _createToolbarContent() {
    const content = document.createElement("div");
    content.className = CSS_CLASSES.toolbarContent;

    const downloadBtn = document.createElement("button");
    downloadBtn.className = CSS_CLASSES.toolbarBtn;
    downloadBtn.textContent = "Download";
    downloadBtn.setAttribute("aria-label", "Open download options");
    downloadBtn.addEventListener("click", () => this._handleDownloadClick());

    const clearBtn = document.createElement("button");
    clearBtn.className = `${CSS_CLASSES.toolbarBtn} secondary`;
    clearBtn.textContent = "Clear";
    clearBtn.setAttribute("aria-label", "Clear all selections");
    clearBtn.addEventListener("click", () => this._handleClearClick());

    content.appendChild(downloadBtn);
    content.appendChild(clearBtn);

    return content;
  }

  /**
   * Set up drag and drop handlers for toolbar
   * @private
   */
  _setupDragHandlers() {
    if (!this.toolbar) return;

    const header = this.toolbar.querySelector(`.${CSS_CLASSES.toolbarHeader}`);
    if (!header) return;

    this.dragHandlers.start = (e) => this._handleDragStart(e);
    this.dragHandlers.move = (e) => this._handleDragMove(e);
    this.dragHandlers.end = () => this._handleDragEnd();

    header.addEventListener("mousedown", this.dragHandlers.start);
  }

  /**
   * Remove drag handlers
   * @private
   */
  _removeDragHandlers() {
    if (this.dragHandlers.start) {
      const header = this.toolbar?.querySelector(
        `.${CSS_CLASSES.toolbarHeader}`
      );
      if (header) {
        header.removeEventListener("mousedown", this.dragHandlers.start);
      }
    }

    document.removeEventListener("mousemove", this.dragHandlers.move);
    document.removeEventListener("mouseup", this.dragHandlers.end);
  }

  /**
   * Handle drag start
   * @param {MouseEvent} e - Mouse event
   * @private
   */
  _handleDragStart(e) {
    if (!this.toolbar) return;

    const rect = this.toolbar.getBoundingClientRect();
    const offset = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };

    this.stateManager.setDragState(true, offset);

    document.addEventListener("mousemove", this.dragHandlers.move);
    document.addEventListener("mouseup", this.dragHandlers.end);

    e.preventDefault();
  }

  /**
   * Handle drag move
   * @param {MouseEvent} e - Mouse event
   * @private
   */
  _handleDragMove(e) {
    const dragState = this.stateManager.getDragState();
    if (!dragState.isDragging || !this.toolbar) return;

    const x = e.clientX - dragState.offset.x;
    const y = e.clientY - dragState.offset.y;

    this.styleManager.updateToolbarPosition(this.toolbar, x, y);
  }

  /**
   * Handle drag end
   * @private
   */
  _handleDragEnd() {
    this.stateManager.setDragState(false);
    document.removeEventListener("mousemove", this.dragHandlers.move);
    document.removeEventListener("mouseup", this.dragHandlers.end);

    // Save position if rememberToolbarPosition is enabled
    if (this.settings.rememberToolbarPosition && this.toolbar) {
      this._saveToolbarPosition();
    }

    // Reset auto-hide timer after drag
    this._startAutoHideTimer();
  }

  /**
   * Save current toolbar position to storage
   * @private
   */
  async _saveToolbarPosition() {
    if (!this.toolbar) return;

    try {
      const rect = this.toolbar.getBoundingClientRect();
      const position = {
        top: rect.top,
        left: rect.left,
      };

      const stored = await StorageService.get(["settings"], "sync");
      const settings = { ...stored.settings, savedToolbarPosition: position };
      await StorageService.set({ settings }, "sync");

      if (EXTENSION_CONFIG.debug?.enabled) {
        console.log(
          `${EXTENSION_CONFIG.logPrefix} Toolbar position saved:`,
          position
        );
      }
    } catch (error) {
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to save toolbar position:`,
        error
      );
    }
  }

  /**
   * Handle close button click
   * @private
   */
  _handleCloseClick() {
    this._clearAutoHideTimer();
    this.stateManager.clearAllSelections();
    this.hideToolbar();
  }

  /**
   * Handle download button click
   * @private
   */
  _handleDownloadClick() {
    this._startAutoHideTimer(); // Reset timer on interaction
    // Send message to background script to open popup
    chrome.runtime
      .sendMessage({
        action: "downloadFromToolbar",
      })
      .catch((error) => {
        console.error(
          `${EXTENSION_CONFIG.logPrefix} Error opening popup:`,
          error
        );
      });
  }

  /**
   * Handle clear button click
   * @private
   */
  _handleClearClick() {
    this._clearAutoHideTimer();
    this.stateManager.clearAllSelections();
    this.updateToolbar().catch(console.error);
  }

  /**
   * Clean up toolbar and remove all event listeners
   */
  cleanup() {
    this._clearAutoHideTimer();
    this._removeDragHandlers();
    this.removeToolbar();

    // Clean up storage listener
    if (chrome.storage && chrome.storage.onChanged && this.storageListener) {
      chrome.storage.onChanged.removeListener(this.storageListener);
    }
  }
}
