/* Enhanced CSS Custom Properties for SVG Switcher Extension */
:root {
  /* Modern Color Palette */
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #a5b4fc;
  --primary-dark: #3730a3;
  --secondary-color: #64748b;
  --secondary-hover: #475569;
  --accent-color: #06b6d4;
  --accent-hover: #0891b2;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --success-color: #10b981;
  --success-hover: #059669;
  --warning-color: #f59e0b;
  --warning-hover: #d97706;

  /* Gradient Colors */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #64748b 0%, #475569 100%);
  --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --gradient-warm: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
  --gradient-cool: linear-gradient(135deg, #10b981 0%, #06b6d4 100%);

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-light: #f0f9ff;
  --bg-glass: rgba(255, 255, 255, 0.8);
  --bg-glass-dark: rgba(15, 23, 42, 0.8);
  --bg-warning: #fef3c7;
  --bg-info: #dbeafe;
  --bg-error: #fee2e2;
  --bg-success: #d1fae5;
  --bg-hover: #f1f5f9;
  --bg-disabled: #f8fafc;
  --bg-card: #ffffff;
  --bg-sidebar: #fafbfc;

  /* Border Colors */
  --border-color: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-light: #f1f5f9;
  --border-warning: #fbbf24;
  --border-info: #60a5fa;
  --border-error: #f87171;
  --border-success: #34d399;
  --border-focus: #6366f1;

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-warning: #92400e;
  --text-info: #1e40af;
  --text-error: #dc2626;
  --text-success: #059669;
  --text-disabled: #cbd5e1;
  --text-inverse: #ffffff;

  /* Enhanced Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  --spacing-3xl: 48px;

  /* Modern Border Radius */
  --border-radius-xs: 2px;
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-2xl: 24px;
  --border-radius-full: 9999px;

  /* Enhanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
  --shadow-colored: 0 10px 15px -3px rgba(99, 102, 241, 0.1), 0 4px 6px -2px rgba(99, 102, 241, 0.05);

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "JetBrains Mono", "Fira Code", Consolas, monospace;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Enhanced Animations */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-spring: 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toolbar: 10000;

  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(10px);
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-light: #1e293b;
  --bg-glass: rgba(15, 23, 42, 0.8);
  --bg-hover: #334155;
  --bg-disabled: #1e293b;
  --bg-card: #1e293b;
  --bg-sidebar: #0f172a;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #64748b;
  --text-disabled: #475569;

  --border-color: #334155;
  --border-secondary: #475569;
  --border-light: #1e293b;

  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);

  --glass-bg: rgba(15, 23, 42, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0.01ms;
    --transition-medium: 0.01ms;
    --transition-slow: 0.01ms;
    --transition-bounce: 0.01ms;
    --transition-spring: 0.01ms;
  }
}
