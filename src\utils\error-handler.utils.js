/**
 * SVG Switcher Extension - Error Handler Utilities
 * Centralized error handling with user-friendly feedback
 */

import {
  EXTENSION_CONFIG,
  DEBUG,
  ERROR_MESSAGES,
} from "../config/constants.js";
import { getElementById, setText, show, hide } from "./dom.utils.js";

/**
 * Error severity levels
 */
export const ERROR_SEVERITY = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  CRITICAL: "critical",
};

/**
 * Error categories for better classification
 */
export const ERROR_CATEGORIES = {
  NETWORK: "network",
  VALIDATION: "validation",
  CONVERSION: "conversion",
  STORAGE: "storage",
  PERMISSION: "permission",
  UNKNOWN: "unknown",
};

/**
 * Main error handler class
 */
export class ErrorHandler {
  static instance = null;

  constructor() {
    if (ErrorHandler.instance) {
      return ErrorHandler.instance;
    }

    this.errorHistory = [];
    this.maxHistorySize = 50;
    ErrorHandler.instance = this;
  }

  /**
   * Get singleton instance
   */
  static getInstance() {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle an error with appropriate logging and user feedback
   * @param {Error|string} error - The error to handle
   * @param {string} category - Error category
   * @param {string} severity - Error severity level
   * @param {string} context - Additional context about where the error occurred
   */
  handleError(
    error,
    category = ERROR_CATEGORIES.UNKNOWN,
    severity = ERROR_SEVERITY.MEDIUM,
    context = ""
  ) {
    const errorInfo = this.processError(error, category, severity, context);

    // Log the error
    this.logError(errorInfo);

    // Store in history
    this.addToHistory(errorInfo);

    // Show user feedback based on severity
    this.showUserFeedback(errorInfo);

    return errorInfo;
  }

  /**
   * Process error into standardized format
   */
  processError(error, category, severity, context) {
    const errorInfo = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : null,
      category,
      severity,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    return errorInfo;
  }

  /**
   * Log error with appropriate level
   */
  logError(errorInfo) {
    const logMessage = `${EXTENSION_CONFIG.logPrefix} Error [${
      errorInfo.category
    }/${errorInfo.severity}] ${
      errorInfo.context ? `(${errorInfo.context})` : ""
    }: ${errorInfo.message}`;

    switch (errorInfo.severity) {
      case ERROR_SEVERITY.CRITICAL:
        console.error(logMessage, errorInfo);
        break;
      case ERROR_SEVERITY.HIGH:
        console.error(logMessage);
        break;
      case ERROR_SEVERITY.MEDIUM:
        console.warn(logMessage);
        break;
      case ERROR_SEVERITY.LOW:
        if (DEBUG.enabled) {
          console.log(logMessage);
        }
        break;
    }
  }

  /**
   * Show appropriate user feedback
   */
  showUserFeedback(errorInfo) {
    const userMessage = this.getUserMessage(errorInfo);

    switch (errorInfo.severity) {
      case ERROR_SEVERITY.CRITICAL:
      case ERROR_SEVERITY.HIGH:
        this.showErrorAlert(userMessage);
        this.updateStatusMessage(userMessage, "error");
        break;
      case ERROR_SEVERITY.MEDIUM:
        this.showErrorToast(userMessage);
        break;
      case ERROR_SEVERITY.LOW:
        // Silent or debug only
        if (DEBUG.enabled) {
          console.log(`${EXTENSION_CONFIG.logPrefix} ${userMessage}`);
        }
        break;
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(errorInfo) {
    // Map common error patterns to user-friendly messages
    const errorMessage = errorInfo.message.toLowerCase();

    if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
      return "Network connection error. Please check your internet connection and try again.";
    }

    if (errorMessage.includes("permission")) {
      return "Permission denied. Please check browser permissions and try again.";
    }

    if (errorMessage.includes("storage")) {
      return "Storage error. Please try clearing extension data or check available storage space.";
    }

    if (
      errorMessage.includes("invalid") ||
      errorMessage.includes("validation")
    ) {
      return "Invalid input. Please check your settings and try again.";
    }

    if (
      errorMessage.includes("conversion") ||
      errorMessage.includes("canvas")
    ) {
      return "Image conversion failed. Please try with a different SVG or settings.";
    }

    // Category-based fallbacks
    switch (errorInfo.category) {
      case ERROR_CATEGORIES.NETWORK:
        return "Network error occurred. Please try again.";
      case ERROR_CATEGORIES.VALIDATION:
        return "Invalid input detected. Please check your settings.";
      case ERROR_CATEGORIES.CONVERSION:
        return "Image conversion failed. Please try again.";
      case ERROR_CATEGORIES.STORAGE:
        return "Storage operation failed. Please try again.";
      case ERROR_CATEGORIES.PERMISSION:
        return "Permission error. Please check browser settings.";
      default:
        return "An unexpected error occurred. Please try again.";
    }
  }

  /**
   * Show error alert dialog
   */
  showErrorAlert(message) {
    // Use native alert for now, could be enhanced with custom modal
    alert(`Error: ${message}`);
  }

  /**
   * Show error toast notification (could be enhanced with custom toast system)
   */
  showErrorToast(message) {
    console.warn(`${EXTENSION_CONFIG.logPrefix} ${message}`);
    // TODO: Implement custom toast notification system
  }

  /**
   * Update status message in UI
   */
  updateStatusMessage(message, type = "error") {
    try {
      const statusElement = getElementById("status");
      if (statusElement) {
        statusElement.className = `status ${type}`;
        setText(statusElement, message);
        show(statusElement);
      }
    } catch (e) {
      // Fallback if DOM manipulation fails
      console.error(
        `${EXTENSION_CONFIG.logPrefix} Failed to update status message:`,
        e
      );
    }
  }

  /**
   * Add error to history
   */
  addToHistory(errorInfo) {
    this.errorHistory.unshift(errorInfo);

    // Limit history size
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get error history for debugging
   */
  getErrorHistory() {
    return [...this.errorHistory];
  }

  /**
   * Clear error history
   */
  clearHistory() {
    this.errorHistory = [];
  }

  /**
   * Check if error is recoverable
   */
  isRecoverable(errorInfo) {
    const recoverableCategories = [
      ERROR_CATEGORIES.NETWORK,
      ERROR_CATEGORIES.VALIDATION,
    ];

    return (
      recoverableCategories.includes(errorInfo.category) &&
      errorInfo.severity !== ERROR_SEVERITY.CRITICAL
    );
  }

  /**
   * Get recovery suggestions for an error
   */
  getRecoverySuggestions(errorInfo) {
    // Define suggestions by category for better maintainability
    const suggestionMap = {
      [ERROR_CATEGORIES.NETWORK]: [
        "Check your internet connection",
        "Try refreshing the page",
      ],
      [ERROR_CATEGORIES.VALIDATION]: [
        "Check your input values",
        "Try different settings",
      ],
      [ERROR_CATEGORIES.CONVERSION]: [
        "Try with a different SVG file",
        "Check SVG file format",
      ],
      [ERROR_CATEGORIES.STORAGE]: [
        "Clear browser storage",
        "Check available disk space",
      ],
      [ERROR_CATEGORIES.PERMISSION]: [
        "Check browser permissions",
        "Try reloading the extension",
      ],
    };

    // Return suggestions for the category or empty array if category not found
    return suggestionMap[errorInfo.category] || [];
  }
}

/**
 * Convenience function to handle errors
 */
export function handleError(error, category, severity, context) {
  const errorHandler = ErrorHandler.getInstance();
  return errorHandler.handleError(error, category, severity, context);
}

/**
 * Convenience function for validation errors
 */
export function handleValidationError(error, context) {
  return handleError(
    error,
    ERROR_CATEGORIES.VALIDATION,
    ERROR_SEVERITY.MEDIUM,
    context
  );
}

/**
 * Convenience function for conversion errors
 */
export function handleConversionError(error, context) {
  return handleError(
    error,
    ERROR_CATEGORIES.CONVERSION,
    ERROR_SEVERITY.HIGH,
    context
  );
}

/**
 * Convenience function for network errors
 */
export function handleNetworkError(error, context) {
  return handleError(
    error,
    ERROR_CATEGORIES.NETWORK,
    ERROR_SEVERITY.MEDIUM,
    context
  );
}

/**
 * Convenience function for storage errors
 */
export function handleStorageError(error, context) {
  return handleError(
    error,
    ERROR_CATEGORIES.STORAGE,
    ERROR_SEVERITY.MEDIUM,
    context
  );
}

/**
 * Convenience function for critical errors
 */
export function handleCriticalError(error, context) {
  return handleError(
    error,
    ERROR_CATEGORIES.UNKNOWN,
    ERROR_SEVERITY.CRITICAL,
    context
  );
}
